"""empty message

Revision ID: 1a9c8dd7d2e4
Revises: 6568d173ebb6
Create Date: 2023-08-25 10:54:19.770418

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '1a9c8dd7d2e4'
down_revision = '6568d173ebb6'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.add_column('monitor_results', sa.Column('publication_number', sa.String(length=50), nullable=True))
    op.drop_constraint('run_id', 'monitor_results', 'unique')
    op.create_unique_constraint('run_id', 'monitor_results', ['run_id', 'family_id', 'publication_number'])


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('run_id', 'monitor_results', 'unique')
    op.create_unique_constraint('run_id', 'monitor_results', ['run_id', 'family_id'])
    op.drop_column('monitor_results', 'publication_number')


def upgrade_auth():
    pass


def downgrade_auth():
    pass
