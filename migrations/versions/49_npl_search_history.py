"""Support history for NPL search

Revision ID: 886bb209995b
Revises: 8c8d75532052
Create Date: 2024-12-02 16:53:02.968669

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '886bb209995b'
down_revision = '8b8b0155ef8b'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.alter_column(
        'search_history',
        'search_type',
        existing_type=sa.Enum('SEMANTIC', 'CITATION', 'BOOLEAN', 'MACHINE_LEARNING'),
        type_=sa.Enum('SEMANTIC', 'CITATION', 'BOOLEAN', 'MACHINE_LEARNING', 'NPL'),
        existing_nullable=True
    )
    op.alter_column(
        'search_history',
        'search_document_type',
        existing_type=sa.Enum('FAMILY', 'PUBLICATION'),
        type_=sa.Enum('FAMILY', 'PUBLICATION', 'PAPER'),
        existing_nullable=True
    )

def downgrade_():
    op.alter_column(
        'search_history',
        'search_type',
        existing_type=sa.Enum('SEMANTIC', 'CITATION', 'BOOLEAN', 'MACHINE_LEARNING', 'NPL'),
        type_=sa.Enum('SEMANTIC', 'CITATION', 'BOOLEAN', 'MACHINE_LEARNING'),
        existing_nullable=True
    )
    op.alter_column(
        'search_history',
        'search_document_type',
        existing_type=sa.Enum('FAMILY', 'PUBLICATION', 'PAPER'),
        type_=sa.Enum('FAMILY', 'PUBLICATION'),
        existing_nullable=True
    )


def upgrade_auth():
    pass


def downgrade_auth():
    pass

