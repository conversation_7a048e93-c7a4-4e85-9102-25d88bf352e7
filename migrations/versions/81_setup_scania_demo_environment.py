"""Setup scania demo environment

Revision ID: 97365a39d55b
Revises: 0a0a5a0014d7
Create Date: 2025-07-23 09:00:14.468781

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from datetime import datetime
import secrets
import string


# revision identifiers, used by Alembic.
revision = '97365a39d55b'
down_revision = '0a0a5a0014d7'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def generate_user_id():
    return ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(10))


def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    
    op.execute(text("""
        INSERT INTO features (short_name, name, description)
        VALUES ('DM', 'Demo', 'Demo feature for testing')
        ON DUPLICATE KEY UPDATE description = VALUES(description)
    """))   


def downgrade_auth():
    """Reverse data from the AUTH database"""

    op.execute(text("""
        DELETE FROM features WHERE short_name = 'DM' AND name = 'Demo'
    """))

