"""empty message

Revision ID: 50074c193666
Revises: 8ae268d6af92
Create Date: 2023-11-06 14:41:03.907525

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '50074c193666'
down_revision = '56dcb2c9bc79'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'translation_api_key')
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('translation_api_key', sa.String(length=256), nullable=True))
    # ### end Alembic commands ###
