"""empty message

Revision ID: 2853e478578f
Revises: 7eadd6316a4d
Create Date: 2024-01-15 16:17:34.598074

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '2853e478578f'
down_revision = '4c3c77ef20b2'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('company_features',
                    sa.Column('company_id', sa.Integer(), nullable=False),
                    sa.Column('feature_id', sa.Integer(), nullable=False),
                    sa.ForeignKeyConstraint(['feature_id'], ['features.id'], ),
                    sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ),
                    sa.PrimaryKeyConstraint('company_id', 'feature_id')
                    )
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('company_features')
    # ### end Alembic commands ###
