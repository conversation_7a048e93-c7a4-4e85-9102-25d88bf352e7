"""empty message

Revision ID: 6568d173ebb6
Revises: a542e67c5e1b
Create Date: 2023-08-14 10:38:58.964167

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '6568d173ebb6'
down_revision = 'a542e67c5e1b'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('monitor_profile', sa.Column('scope', sa.Enum('Families', 'Publications', 'Applications', 'Grants', 'UtilityModels', 'RegisteredDesigns', name='monitorprofilescope'), nullable=False))
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('monitor_profile', 'scope')
    # ### end Alembic commands ###


def upgrade_auth():
    pass


def downgrade_auth():
    pass
