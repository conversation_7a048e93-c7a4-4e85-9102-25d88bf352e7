"""Add settings templates for users

Revision ID: 16b21f039729
Revises: e2fe81f20ba9
Create Date: 2025-01-08 17:36:42.155465

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '16b21f039729'
down_revision = 'd2b9ad560db0'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()





def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('settings_templates',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('domain', sa.String(length=128), nullable=False),
    sa.Column('ui_settings', sa.JSON(), nullable=True),
    sa.Column('subscription_type', sa.Enum('FREE', 'TRIAL', 'BASIC', 'COLLABORATOR', 'PROFESSIONAL', 'ENTERPRISE', name='subscriptiontype'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('settings_templates_features',
    sa.Column('settings_template_id', sa.Integer(), nullable=False),
    sa.Column('feature_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['feature_id'], ['features.id'], ),
    sa.ForeignKeyConstraint(['settings_template_id'], ['settings_templates.id'], ),
    sa.PrimaryKeyConstraint('settings_template_id', 'feature_id')
    )


def downgrade_auth():
    op.drop_table('settings_templates_features')
    op.drop_table('settings_templates')

