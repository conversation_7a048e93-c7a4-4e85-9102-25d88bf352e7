"""Migrate tasks to ratings

Revision ID: 62820ef1ef31
Revises: d2b9ad560db0
Create Date: 2025-01-02 06:22:31.201256

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = '62820ef1ef31'
down_revision = '7b529e3726e3'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    session = Session(bind=op.get_bind())

    # Handle TEXT_REPLY tasks
    # First create document comments for TEXT_REPLY tasks
    session.execute(sa.text("""
        INSERT INTO documents_comments (
            comment, start_pos, end_pos, created_at, updated_at,  user_id, company_id, document_id, text, color, 
            field, source
        )
        SELECT 
            ta.answer as comment,
            0 as start_pos,
            0 as end_pos,
            ta.answered_at as created_at,
            ta.answered_at as updated_at,
            ta.answered_by as user_id,
            t.company_id,
            ta.document_id,
            '' as text,
            '' as color,
            'general' as field,
            'general' as source
        FROM task_assignment ta
        JOIN task t ON ta.task_id = t.id
        WHERE t.task_type = 'TEXT_REPLY'
        AND ta.answer IS NOT NULL
    """))

    # Redirect notification of TEXT_REPLY task to patent viewer comment section
    session.execute(sa.text("""
        UPDATE notifications n
        JOIN task t ON n.resource_id = t.id AND n.resource_type = 'TASK'
        JOIN task_assignment ta ON ta.task_id = t.id
        SET n.resource_type = 'PATENT' , n.url = CONCAT('/patent/view/', ta.document_id, '?comment_id=0'), n.resource_id = ta.document_id
        WHERE t.task_type = 'TEXT_REPLY'
    """))

    # Then delete TEXT_REPLY task assignments and tasks
    session.execute(sa.text("""
        DELETE ta FROM task_assignment ta
        JOIN task t ON ta.task_id = t.id
        WHERE t.task_type = 'TEXT_REPLY'
    """))

    session.execute(sa.text("""
        DELETE FROM task 
        WHERE task_type = 'TEXT_REPLY'
    """))

    # Handle YES_NO_ANSWER tasks
    # Convert Yes to 5 stars, No to 1 star
    session.execute(sa.text("""
        UPDATE task_assignment ta
        JOIN task t ON ta.task_id = t.id
        SET ta.answer = CASE 
            WHEN ta.answer = 'YES' THEN '5'
            WHEN ta.answer = 'NO' THEN '1'
            ELSE ta.answer
        END
        WHERE t.task_type = 'YES_NO_ANSWER'
    """))

    # Update task type to STAR_RATING
    session.execute(sa.text("""
        UPDATE task
        SET task_type = 'STAR_RATING'
        WHERE task_type = 'YES_NO_ANSWER'
    """))
    # Handle LABELS tasks
    # Redirect notification of LABELS task to patent viewer
    session.execute(sa.text("""
        UPDATE notifications n
        JOIN task t ON n.resource_id = t.id AND n.resource_type = 'TASK'
        JOIN task_assignment ta ON ta.task_id = t.id
        SET n.resource_type = 'PATENT' , n.url = CONCAT('/patent/view/', ta.document_id), n.resource_id = ta.document_id
        WHERE t.task_type = 'LABELS'
    """))

    # Delete LABELS tasks
    session.execute(sa.text("""
        DELETE ta FROM task_assignment ta
        JOIN task t ON ta.task_id = t.id
        WHERE t.task_type = 'LABELS'
    """))

    session.execute(sa.text("""
        DELETE FROM task
        WHERE task_type = 'LABELS'
    """))


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
