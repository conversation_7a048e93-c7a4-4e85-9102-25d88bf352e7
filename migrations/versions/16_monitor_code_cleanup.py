"""Monitor code cleanup

Revision ID: 8f7289cae289
Revises: c83bf3613653
Create Date: 2023-09-29 08:16:10.697976

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '8f7289cae289'
down_revision = 'c83bf3613653'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('monitor_profile', 'machine_learning_training_info')
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('monitor_profile', sa.Column('machine_learning_training_info', mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'), nullable=True))
    # ### end Alembic commands ###


def upgrade_auth():
    pass


def downgrade_auth():
    pass
