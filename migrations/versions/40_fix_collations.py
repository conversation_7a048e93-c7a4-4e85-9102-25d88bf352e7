"""standardize charset and collation

Revision ID: 9236a374a7de
Revises: ff021ed7d3a4
Create Date: 2024-10-10 16:15:05.357812

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '9236a374a7de'
down_revision = 'c6367b9249bd'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    fix_collations("octimine_web")


def downgrade_():
    pass


def upgrade_auth():
    fix_collations("octimine_auth")


def downgrade_auth():
    pass


def fix_collations(db_name):
    op.execute(sa.text(f'''
ALTER DATABASE {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    '''))
    alter_statements = op.get_bind().execute(sa.text(f'''
SELECT CONCAT('ALTER TABLE ', table_name, ' CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;')
FROM information_schema.tables
WHERE table_schema = '{db_name}' AND table_type = 'BASE TABLE';
    '''))
    for statement, in alter_statements:
        op.execute(sa.text(statement))

