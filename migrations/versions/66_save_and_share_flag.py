"""save and share disable flag for swiss patent office

Revision ID: 225faeba11ad
Revises: 307ac325971e
Create Date: 2025-03-25 12:57:49.759951

"""
from alembic import op
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = '225faeba11ad'
down_revision = '307ac325971e'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    op.execute("""INSERT INTO features (short_name, name) VALUES ('SP', 'Disable save and share')""")


def downgrade_auth():
    op.execute(text("""DELETE FROM features WHERE short_name = 'SP'"""))
