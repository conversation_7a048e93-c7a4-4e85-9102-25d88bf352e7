"""Add notification_emails

Revision ID: d2b9ad560db0
Revises: e2fe81f20ba9
Create Date: 2024-12-09 08:23:01.995159

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'd2b9ad560db0'
down_revision = 'e2fe81f20ba9'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('notification_emails',
                    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
                    sa.Column('send_at', sa.DateTime(), nullable=False),
                    sa.Column('group_key', sa.String(255), nullable=False, index=True),
                    sa.Column('from_email', sa.String(255), nullable=True),
                    sa.Column('to_email', sa.String(255), nullable=False),
                    sa.Column('subject', sa.String(255), nullable=False),
                    sa.Column('template', sa.String(255), nullable=False),
                    sa.Column('params', sa.JSON(), nullable=False),
                    sa.Column('status', sa.Enum('PENDING', 'SENDING', 'SENT', 'FAILED',
                                                name='notification_email_status'), nullable=False),
                    sa.Column('from_user_id', sa.Integer(), nullable=True),
                    sa.Column('to_user_id', sa.Integer(), nullable=False),
                    sa.Column('notification_id', sa.Integer(), nullable=True),
                    sa.Column('created_at', sa.DateTime(), nullable=False),
                    sa.PrimaryKeyConstraint('id'),
                    )

    op.create_foreign_key(op.f('fk_notification_emails_notification_id'), 'notification_emails', 'notifications',
                          ['notification_id'], ['id'], ondelete='CASCADE')
    op.add_column('notifications', sa.Column('custom_source', sa.String(255), nullable=True))
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_notification_emails_notification_id'), 'notification_emails', type_='foreignkey')
    op.drop_table('notification_emails')
    op.drop_column('notifications', 'custom_source')
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
