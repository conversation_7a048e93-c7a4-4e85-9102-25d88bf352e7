"""add expanded queries to history

Revision ID: b0922c2c9fc5
Revises: 4fcb530fda3f
Create Date: 2025-03-31 17:27:44.566802

"""
from sqlalchemy import text
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'b0922c2c9fc5'
down_revision = '05c2b4438a82'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    with op.batch_alter_table('search_history', schema=None) as batch_op:
        batch_op.add_column(sa.Column('expand_search_input', sa.Boolean(), nullable=True, default=False))
        batch_op.add_column(sa.Column('expanded_search_input', sa.Text().with_variant(mysql.MEDIUMTEXT(), 'mysql'), nullable=True))

def downgrade_():
    with op.batch_alter_table('search_history', schema=None) as batch_op:
        batch_op.drop_column('expanded_search_input')
        batch_op.drop_column('expand_search_input')


def upgrade_auth():
    op.execute(text("""DELETE from user_features WHERE feature_id in 
                    (SELECT id FROM features WHERE short_name = 'QE')"""))
    op.execute(text("""DELETE FROM company_features WHERE feature_id in 
                    (SELECT id FROM features WHERE short_name = 'QE')"""))
    op.execute(text("""DELETE FROM features WHERE short_name = 'QE'"""))


def downgrade_auth():
    op.execute("""INSERT INTO features (short_name, name) VALUES ('QE', 'Query Expansion')""")
