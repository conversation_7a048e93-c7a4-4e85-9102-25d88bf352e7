"""Allow enabling AI tagger for tags

Revision ID: f93617a64081
Revises: 70b1aa6db9b8
Create Date: 2024-07-02 14:33:14.179211

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'f93617a64081'
down_revision = '70b1aa6db9b8'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.add_column('tags', sa.Column('ai_enabled', sa.Boolean(), nullable=True))
    op.execute('UPDATE tags SET ai_enabled = 0')
    op.alter_column('tags', 'ai_enabled', existing_type=sa.Boolean(), nullable=False)

def downgrade_():
    op.drop_column('tags', 'ai_enabled')

def upgrade_auth():
    pass


def downgrade_auth():
    pass
