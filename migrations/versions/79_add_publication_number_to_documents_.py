"""Add publication_number to documents_labels

Revision ID: dd7a797de583
Revises: 18891546b0c2
Create Date: 2025-07-08 11:45:44.267479

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'dd7a797de583'
down_revision = '18891546b0c2'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.add_column('documents_labels', sa.Column('publication_number', sa.String(length=50), nullable=True))


def downgrade_():
    op.drop_column('documents_labels', 'publication_number')


def upgrade_auth():
    pass


def downgrade_auth():
    pass
