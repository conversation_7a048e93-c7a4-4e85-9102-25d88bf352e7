"""Clean up notification emails

Revision ID: af5ae7c76c3a
Revises: 2bd48db5435d
Create Date: 2025-02-17 21:29:02.482300

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'af5ae7c76c3a'
down_revision = '2bd48db5435d'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():

    with op.batch_alter_table('notification_emails', schema=None) as batch_op:
        batch_op.drop_column('to_user_id')
        batch_op.drop_column('from_user_id')

    op.add_column('notifications', sa.Column('visible', sa.Boolean(), nullable=True))
    op.execute('UPDATE notifications SET visible = 1')
    op.alter_column('notifications', 'visible', existing_type=sa.Boolean(), nullable=False)
    op.drop_column('notifications', 'custom_source')
    op.add_column('notifications', sa.Column('from_group_id', sa.Integer(), nullable=True))
    op.add_column('notifications', sa.Column('to_group_id', sa.Integer(), nullable=True))


def downgrade_():
    with op.batch_alter_table('notifications', schema=None) as batch_op:
        batch_op.drop_column('from_group_id')
        batch_op.drop_column('to_group_id')
        batch_op.drop_column('visible')
        batch_op.add_column(sa.Column('custom_source', sa.String(255), nullable=True))

    with op.batch_alter_table('notification_emails', schema=None) as batch_op:
        batch_op.add_column(sa.Column('from_user_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('to_user_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False))


def upgrade_auth():
    pass


def downgrade_auth():
    pass
