"""empty message

Revision ID: ec809d461c82
Revises: 8f7289cae289
Create Date: 2023-10-12 20:34:45.016364

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'ec809d461c82'
down_revision = '8f7289cae289'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('synonyms')
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('synonyms',
                    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
                    sa.Column('created_at', sa.DateTime(), nullable=False),
                    sa.Column('updated_at', sa.DateTime(), nullable=False),
                    sa.Column('user_id', sa.String(length=16), nullable=False),
                    sa.Column('name', sa.String(length=256), nullable=False),
                    sa.Column('synset', sa.JSON(), nullable=True),
                    sa.PrimaryKeyConstraint('id')
                    )
    # ### end Alembic commands ###


def upgrade_auth():
    pass


def downgrade_auth():
    pass
