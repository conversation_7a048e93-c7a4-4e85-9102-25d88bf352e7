"""tag collections

Revision ID: 511c5c081c6d
Revises: 12e1e4991c74
Create Date: 2023-05-19 16:11:08.485687

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '511c5c081c6d'
down_revision = '50074c193666'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.drop_index('ix_tag_documents_company_id', table_name='tag_documents')
    op.drop_index('ix_tag_documents_user_id', table_name='tag_documents')
    op.drop_table('tag_documents')
    op.add_column('tags', sa.Column('collection_id', sa.Integer(), nullable=True))
    op.create_foreign_key('tags_ibfk_1', 'tags', 'collections', ['collection_id'], ['id'], ondelete='CASCADE')
    op.alter_column('collections',
                    'source_type',
                    existing_type=sa.Enum(
                        'SEMANTIC_SEARCH_HISTORY',
                        'BOOLEAN_SEARCH_HISTORY',
                        'CITATION_SEARCH_HISTORY',
                        'MONITOR_RUN',
                        'MACHINE_LEARNING'
                    ),
                    type_=sa.Enum(
                        'SEMANTIC_SEARCH_HISTORY',
                        'BOOLEAN_SEARCH_HISTORY',
                        'CITATION_SEARCH_HISTORY',
                        'MONITOR_RUN',
                        'MACHINE_LEARNING',
                        'TAG'
                    ),
                    existing_nullable=True),
    op.alter_column('collaborations',
                    'collaborator_type',
                    existing_type=sa.Enum(
                        'USER',
                        'GROUP'
                    ),
                    type_=sa.Enum(
                        'USER',
                        'GROUP',
                        'COMPANY'
                    ),
                    existing_nullable=False)


def downgrade_():
    op.drop_constraint('tags_ibfk_1', 'tags', type_='foreignkey')
    op.drop_column('tags', 'collection_id')
    op.create_table(
        'tag_documents',
        sa.Column('id', mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
        sa.Column('tag_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
        sa.Column('document_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
        sa.Column('user_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
        sa.Column('company_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], name='tag_documents_ibfk_1', ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        mysql_default_charset='utf8mb4',
        mysql_engine='InnoDB'
    )
    op.create_index('ix_tag_documents_user_id', 'tag_documents', ['user_id'], unique=False)
    op.create_index('ix_tag_documents_company_id', 'tag_documents', ['company_id'], unique=False)
    op.alter_column('collections',
                    'source_type',
                    existing_type=sa.Enum(
                        'SEMANTIC_SEARCH_HISTORY',
                        'BOOLEAN_SEARCH_HISTORY',
                        'CITATION_SEARCH_HISTORY',
                        'MONITOR_RUN',
                        'MACHINE_LEARNING',
                        'TAG'
                    ),
                    type_=sa.Enum(
                        'SEMANTIC_SEARCH_HISTORY',
                        'BOOLEAN_SEARCH_HISTORY',
                        'CITATION_SEARCH_HISTORY',
                        'MONITOR_RUN',
                        'MACHINE_LEARNING',
                    ),
                    existing_nullable=True)

    op.alter_column('collaborations',
                    'collaborator_type',
                    existing_type=sa.Enum(
                        'USER',
                        'GROUP',
                        'COMPANY'
                    ),
                    type_=sa.Enum(
                        'USER',
                        'GROUP',
                    ),
                    existing_nullable=False)


def upgrade_auth():
    pass


def downgrade_auth():
    pass
