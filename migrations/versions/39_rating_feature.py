"""empty message

Revision ID: c6367b9249bd
Revises: ff021ed7d3a4
Create Date: 2024-10-10 16:06:55.377701

"""
from alembic import op
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = 'c6367b9249bd'
down_revision = 'ff021ed7d3a4'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""INSERT INTO features (short_name, name) VALUES ('R', 'Rating')""")
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(text("""DELETE FROM features WHERE short_name = 'R'"""))
    # ### end Alembic commands ###
