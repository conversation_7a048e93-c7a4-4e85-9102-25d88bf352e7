"""empty message

Revision ID: 0225089ef133
Revises: 7eadd6316a4d
Create Date: 2024-01-16 14:24:29.936253

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '0225089ef133'
down_revision = '2853e478578f'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('landscape_profiles', sa.Column('competitive_profile_id', sa.Integer(), nullable=True))
    op.add_column('landscape_profiles', sa.Column('is_competitive_profile', sa.<PERSON>an(), nullable=False,
                                                  default=False))
    op.create_foreign_key(op.f('fk_landscape_competitive_profile_id'), 'landscape_profiles', 'landscape_profiles',
                          ['competitive_profile_id'], ['id'])
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_landscape_competitive_profile_id'), 'landscape_profiles', type_='foreignkey')
    op.drop_column('landscape_profiles', 'competitive_profile_id')
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

