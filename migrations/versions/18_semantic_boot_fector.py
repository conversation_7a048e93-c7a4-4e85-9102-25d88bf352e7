"""Added semantic search boost_factors params in web api

Revision ID: 06fdda999c8b
Revises: ec809d461c82
Create Date: 2023-10-19 09:27:58.163251

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '06fdda999c8b'
down_revision = 'ec809d461c82'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('search_history', sa.Column('boost_factors', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('search_history', 'boost_factors')
    # ### end Alembic commands ###


def upgrade_auth():
    pass


def downgrade_auth():
    pass
