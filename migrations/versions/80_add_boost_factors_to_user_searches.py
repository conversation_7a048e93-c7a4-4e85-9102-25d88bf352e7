"""Add boost_factors to user_searches

Revision ID: 0a0a5a0014d7
Revises: dd7a797de583
Create Date: 2025-07-14 11:29:09.147478

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '0a0a5a0014d7'
down_revision = 'dd7a797de583'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    op.add_column('user_searches',
                  sa.Column('boost_factors', sa.Text().with_variant(mysql.MEDIUMTEXT(), 'mysql'), nullable=True))
    op.add_column('user_recurring_searches',
                  sa.Column('boost_factors', sa.Text().with_variant(mysql.MEDIUMTEXT(), 'mysql'), nullable=True))


def downgrade_auth():
    op.drop_column('user_searches', 'boost_factors')
    op.drop_column('user_recurring_searches', 'boost_factors')
