"""collection_id relation key index in MonitorProfileModel

Revision ID: 05c2b4438a82
Revises: be662a254675
Create Date: 2025-04-08 14:24:42.304342

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '05c2b4438a82'
down_revision = 'be662a254675'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()





def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('monitor_profile', schema=None) as batch_op:
        batch_op.create_foreign_key('collection_idfk_1', 'collections', ['collection_id'], ['id'], ondelete='SET NULL')
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('monitor_profile', schema=None) as batch_op:
        batch_op.drop_constraint('collection_idfk_1', type_='foreignkey')

    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

