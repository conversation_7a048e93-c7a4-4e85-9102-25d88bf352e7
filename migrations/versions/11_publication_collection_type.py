"""Adjust publication number in result collection

Revision ID: a542e67c5e1b
Revises: 5cae59812ae4
Create Date: 2023-08-03 14:19:03.309042

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = 'a542e67c5e1b'
down_revision = '5cae59812ae4'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('collection_results', sa.Column('publication_number', sa.String(length=50), nullable=True))
    op.alter_column('collection_results', 'document_id',
                    existing_type=mysql.INTEGER(display_width=11),
                    nullable=True)
    op.create_check_constraint(
        "ck_must_have_patent_reference",
        "collection_results",
        'NOT(collection_results.document_id IS NULL AND collection_results.publication_number IS NULL)',
    )
    op.add_column('collections', sa.Column('collection_type', sa.Enum('FAMILY', 'PUBLICATION', name='collectiontype'), nullable=True))
    op.execute(text(''' update collections cols set cols.collection_type = 'FAMILY' '''))
    op.alter_column('collections', 'collection_type', existing_type=sa.Enum('FAMILY', 'PUBLICATION'), nullable=False)
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('ck_must_have_patent_reference', 'collection_results', type_="check")
    op.drop_column('collections', 'collection_type')
    op.execute(text(''' DELETE FROM collection_results  WHERE document_id is null '''))
    op.alter_column('collection_results', 'document_id',
                    existing_type=mysql.INTEGER(display_width=11), nullable=False)
    op.drop_column('collection_results', 'publication_number')
    # ### end Alembic commands ###


def upgrade_auth():
    pass


def downgrade_auth():
    pass
