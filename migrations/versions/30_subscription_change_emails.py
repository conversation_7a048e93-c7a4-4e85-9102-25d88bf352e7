"""Migrate trial emails to more generic subscription change emails

Revision ID: aca42f6452b3
Revises: 4609369ce47f
Create Date: 2024-05-29 14:36:39.840228

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import select
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = 'aca42f6452b3'
down_revision = '4609369ce47f'
branch_labels = None
depends_on = None


Base = declarative_base()

class TrialEmail(Base):

    __tablename__ = 'trial_emails'

    id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    send_at = sa.Column(sa.DateTime(), nullable=False)
    user_id = sa.Column(sa.Integer, nullable=False)
    params = sa.Column(sa.JSON(), nullable=False)

class SubscriptionChangeEmail(Base):

    __tablename__ = 'subscription_change_emails'

    id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    from_type = sa.Column(sa.String(50), nullable=True)
    to_type = sa.Column(sa.String(50), nullable=False)
    send_at = sa.Column(sa.DateTime(), nullable=False)
    user_id = sa.Column(sa.Integer, nullable=False)
    params = sa.Column(sa.JSON(), nullable=False)


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()

def upgrade_():
    pass

def downgrade_():
    pass

def upgrade_auth():
    session = Session(bind=op.get_bind())
    op.create_table(
        'subscription_change_emails',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('from_type', sa.String(50), nullable=True),
        sa.Column('to_type', sa.String(50), nullable=False),
        sa.Column('send_at', sa.DateTime(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('params', sa.JSON(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    trial_emails = session.execute(select(TrialEmail)).scalars().all()
    for te in trial_emails:
        session.add(
            SubscriptionChangeEmail(
                send_at=te.send_at,
                user_id=te.user_id,
                from_type=None,
                to_type='TRIAL',
                params={
                    'subject': 'Your Octimine trial period just started',
                    'template': 'trial_user',
                    'params': te.params,
                }
            )
        )
    session.commit()
    op.drop_table('trial_emails')

def downgrade_auth():
    session = Session(bind=op.get_bind())
    op.create_table(
        'trial_emails',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('send_at', sa.DateTime(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('params', sa.JSON(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    trial_emails = session.execute(
        select(SubscriptionChangeEmail).
        where(SubscriptionChangeEmail.to_type == 'TRIAL')
    ).scalars().all()
    for te in trial_emails:
        session.add(
            TrialEmail(
                send_at=te.send_at,
                user_id=te.user_id,
                params=te.params.get('params') or {}
            ) 
        )
    session.commit()
    op.drop_table('subscription_change_emails')
