"""empty message

Revision ID: dd005255ded9
Revises: 2bd48db5435d
Create Date: 2025-02-21 14:12:48.516522

"""
from alembic import op
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = 'dd005255ded9'
down_revision = '3d97250b887f'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""INSERT INTO features (short_name, name) VALUES ('QE', 'Query Expansion')""")
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(text("""DELETE FROM features WHERE short_name = 'QE'"""))
    # ### end Alembic commands ###

