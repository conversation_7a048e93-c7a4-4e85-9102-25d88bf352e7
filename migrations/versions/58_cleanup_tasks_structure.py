"""Drop unused columns from task and task_assignment

Revision ID: 2bd48db5435d
Revises: 1bd48db5435d
Create Date: 2025-02-18 10:24:50.843731

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '2bd48db5435d'
down_revision = '1bd48db5435d'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():

    with op.batch_alter_table('task', schema=None) as batch_op:
        batch_op.drop_index('ix_task_resource_id')
        batch_op.drop_column('resource_id')
        batch_op.drop_column('resource_type')
        batch_op.alter_column('document_id', existing_type=sa.Integer(), nullable=False)
        batch_op.alter_column(
            'task_type',
            existing_type=sa.Enum('STAR_RATING', 'YES_NO_ANSWER', 'TEXT_REPLY', 'LABELS', name='taskanswertypeenum'),
            type_=sa.Enum('STAR_RATING', name='taskanswertypeenum'),
            existing_nullable=False
        )

    with op.batch_alter_table('task_assignment', schema=None) as batch_op:
        batch_op.drop_column('document_id')


def downgrade_():
    with op.batch_alter_table('task_assignment', schema=None) as batch_op:
        batch_op.add_column(sa.Column('document_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))

    with op.batch_alter_table('task', schema=None) as batch_op:
        batch_op.alter_column(
            'task_type',
            existing_type=sa.Enum('STAR_RATING', name='taskanswertypeenum'),
            type_=sa.Enum('STAR_RATING', 'YES_NO_ANSWER', 'TEXT_REPLY', 'LABELS', name='taskanswertypeenum'),
            existing_nullable=False
        )
        batch_op.alter_column('document_id', existing_type=sa.Integer(), nullable=True)
        batch_op.add_column(sa.Column('resource_type', mysql.ENUM('DOCUMENT', 'COLLECTION', 'MONITOR_RUN'), nullable=True))
        batch_op.add_column(sa.Column('resource_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
        batch_op.create_index('ix_task_resource_id', ['resource_id'], unique=False)

def upgrade_auth():
    pass


def downgrade_auth():
    pass
