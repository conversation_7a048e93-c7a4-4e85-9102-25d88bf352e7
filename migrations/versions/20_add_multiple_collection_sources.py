"""Add multiple sources for a collection

Revision ID: 4c3c77ef20b2
Revises: 7eadd6316a4d
Create Date: 2023-12-15 10:51:54.099944

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '4c3c77ef20b2'
down_revision = '7eadd6316a4d'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('collection_source',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('search_history_id', sa.Integer(), nullable=True),
    sa.<PERSON>umn('monitor_run_id', sa.Integer(), nullable=True),
    sa.Column('collection_id', sa.Integer(), nullable=False),
    sa.CheckConstraint('NOT(search_history_id IS NULL AND monitor_run_id IS NULL)'),
    sa.ForeignKeyConstraint(['collection_id'], ['collections.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_collection_source_user_id'), 'collection_source', ['user_id'], unique=False)
    op.create_table('collection_result_source',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('score', sa.Float(), nullable=True),
    sa.Column('collection_source_id', sa.Integer(), nullable=False),
    sa.Column('collection_result_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['collection_result_id'], ['collection_results.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['collection_source_id'], ['collection_source.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.execute(sa.text(f"""
       INSERT INTO collection_source (user_id, search_history_id, collection_id, created_at, updated_at) 
       SELECT collections.user_id, collections.source_id, collections.id, collections.created_at, collections.updated_at
        FROM collections 
        WHERE collections.source_id IS  NOT  NULL and 
            (collections.source_type = 'SEMANTIC_SEARCH_HISTORY' or 
             collections.source_type = 'BOOLEAN_SEARCH_HISTORY' or
             collections.source_type = 'CITATION_SEARCH_HISTORY')
    """))
    op.execute(sa.text(f"""
       INSERT INTO collection_source (user_id, monitor_run_id, collection_id, created_at, updated_at) 
       SELECT collections.user_id, collections.source_id, collections.id, collections.created_at, collections.updated_at
        FROM collections 
        WHERE collections.source_type = 'MONITOR_RUN' and collections.source_id IS  NOT  NULL 
    """))
    op.drop_column('collections', 'source_type')
    op.drop_column('collections', 'source_id')
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('collections', sa.Column('source_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('collections', sa.Column('source_type', mysql.ENUM('SEMANTIC_SEARCH_HISTORY', 'BOOLEAN_SEARCH_HISTORY', 'CITATION_SEARCH_HISTORY', 'MONITOR_RUN', 'MACHINE_LEARNING', 'TAG'), nullable=True))
    op.drop_index(op.f('ix_collection_results_user_id'), table_name='collection_results')
    op.drop_table('collection_result_source')
    op.drop_index(op.f('ix_collection_source_user_id'), table_name='collection_source')
    op.drop_table('collection_source')
    # ### end Alembic commands ###


def upgrade_auth():
    pass


def downgrade_auth():
    pass
