"""Nested folders

Revision ID: c23a62f393eb
Revises: b0922c2c9fc5
Create Date: 2025-04-09 17:19:28.376307

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'c23a62f393eb'
down_revision = 'b0922c2c9fc5'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.add_column('folders', sa.Column('parent_id', sa.Integer(), nullable=True))
    op.create_foreign_key(op.f('fk_parent_id'), 'folders', 'folders', ['parent_id'], ['id'], ondelete='CASCADE')

def downgrade_():
    op.drop_constraint('fk_parent_id', 'folders', type_='foreignkey')
    op.drop_column('folders', 'parent_id')


def upgrade_auth():
    pass


def downgrade_auth():
    pass
