"""collection id reference for monitor profile results

Revision ID: 307ac325971e
Revises: fb3cab14bc7f
Create Date: 2025-03-12 13:15:04.202162

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '307ac325971e'
down_revision = 'fb3cab14bc7f'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()





def upgrade_():
    with op.batch_alter_table('monitor_profile', schema=None) as batch_op:
        batch_op.add_column(sa.Column('collection_id', sa.Integer(), nullable=True))


def downgrade_():
    with op.batch_alter_table('monitor_profile', schema=None) as batch_op:
        batch_op.drop_column('collection_id')


def upgrade_auth():
    pass


def downgrade_auth():
    pass

