"""empty message

Revision ID: 8ae268d6af92
Revises: 43e4e2c79eaa
Create Date: 2023-09-11 15:35:52.026967

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '8ae268d6af92'
down_revision = '43e4e2c79eaa'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    op.execute(text("""delete from user_features where feature_id in 
                    (select id from features where short_name = 'NM')"""))
    op.execute(text("""delete from features where short_name = 'NM'"""))


def downgrade_auth():
    pass
