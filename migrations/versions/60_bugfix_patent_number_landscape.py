"""Add columns for family and publication count

Revision ID: 174857c3c57d
Revises: af5ae7c76c3a
Create Date: 2025-02-06 10:57:23.486012

"""

from alembic import op
import sqlalchemy as sa



# revision identifiers, used by Alembic.
revision = '174857c3c57d'
down_revision = 'af5ae7c76c3a'
# down   '
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()

def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():

    with op.batch_alter_table('landscape_profiles', schema=None) as batch_op:
        batch_op.add_column(sa.Column('families_count', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('publications_count', sa.Integer(), nullable=True))


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###



    with op.batch_alter_table('landscape_profiles', schema=None) as batch_op:
        batch_op.drop_column('publications_count')
        batch_op.drop_column('families_count')


    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

if __name__ == "__main__":
    upgrade_()
