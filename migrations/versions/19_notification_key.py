"""empty message

Revision ID: 7eadd6316a4d
Revises: 06fdda999c8b
Create Date: 2023-12-20 15:59:18.495583

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '7eadd6316a4d'
down_revision = '06fdda999c8b'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.add_column('notifications', sa.Column('key', sa.String(length=256), nullable=True))
    op.create_index(op.f('ix_notifications_key'), 'notifications', ['key'], unique=False)


def downgrade_():
    op.drop_index(op.f('ix_notifications_key'), table_name='notifications')
    op.drop_column('notifications', 'key')


def upgrade_auth():
    pass


def downgrade_auth():
    pass
