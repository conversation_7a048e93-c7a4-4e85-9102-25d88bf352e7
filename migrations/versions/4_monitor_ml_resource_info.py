"""monitor machine learning resource info

Revision ID: 56dcb2c9bc79
Revises: 12e1e4991c74
Create Date: 2023-05-24 16:00:56.094562

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '56dcb2c9bc79'
down_revision = '12e1e4991c74'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.add_column('monitor_profile', sa.Column('machine_learning_resource_info', sa.JSON(), nullable=True))


def downgrade_():
    op.drop_column('monitor_profile', 'machine_learning_resource_info')


def upgrade_auth():
    pass


def downgrade_auth():
    pass


