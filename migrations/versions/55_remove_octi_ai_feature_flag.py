"""Remove octi ai feature flag

Revision ID: bcd385358fb3
Revises: 62820ef1ef31
Create Date: 2025-01-27 06:44:07.751455

"""
from alembic import op
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = 'bcd385358fb3'
down_revision = '62820ef1ef31'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(text("""delete from user_features where feature_id in 
                    (select id from features where short_name = 'AI')"""))
    op.execute(text("""delete from company_features where feature_id in 
                    (select id from features where short_name = 'AI')"""))
    op.execute(text("""DELETE FROM features WHERE short_name = 'AI'"""))
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""INSERT INTO features (short_name, name) VALUES ('AI', 'AI assistant')""")
    # ### end Alembic commands ###

