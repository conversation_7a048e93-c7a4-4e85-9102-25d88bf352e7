"""empty message

Revision ID: fe89bfd4d499
Revises: bbdfdf51c8a3
Create Date: 2024-11-04 11:29:22.430500

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'fe89bfd4d499'
down_revision = 'bbdfdf51c8a3'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.alter_column(
        'task',
        'status',
        existing_type=sa.Enum('OPEN', 'CLOSED', 'DONE', name='taskstatusenum'),
        existing_nullable=False,
        type_=sa.Enum('NEW', 'OPEN', 'CLOSED', 'DONE', name='taskstatusenum')
    )
    op.alter_column(
        'task_assignment',
        'status',
        existing_type=sa.Enum('OPEN', 'CLOSED', 'DONE', 'OVERDUE', name='assignedtaskstatusenum'),
        existing_nullable=False,
        type_=sa.Enum('NEW', 'OPEN', 'CLOSED', 'DONE', 'OVERDUE', name='assignedtaskstatusenum'),
    )


def downgrade_():
    op.alter_column(
        'task',
        'status',
        existing_type=sa.Enum('NEW', 'OPEN', 'CLOSED', 'DONE', name='taskstatusenum'),
        existing_nullable=False,
        type_=sa.Enum('OPEN', 'CLOSED', 'DONE', name='taskstatusenum')
    )
    op.alter_column(
        'task_assignment',
        'status',
        existing_type=sa.Enum('NEW', 'OPEN', 'CLOSED', 'DONE', 'OVERDUE', name='assignedtaskstatusenum'),
        existing_nullable=False,
        type_=sa.Enum('OPEN', 'CLOSED', 'DONE', 'OVERDUE', name='assignedtaskstatusenum'),
    )


def upgrade_auth():
    pass


def downgrade_auth():
    pass

