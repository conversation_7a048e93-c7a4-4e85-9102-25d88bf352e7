"""Octi AI featrue flag 

Revision ID: e2fe81f20ba9
Revises: 886bb209995b
Create Date: 2024-12-23 13:40:31.306304

"""
from alembic import op
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = 'e2fe81f20ba9'
down_revision = '886bb209995b'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()





def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""INSERT INTO features (short_name, name) VALUES ('AI', 'AI assistant')""")
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(text("""DELETE FROM features WHERE short_name = 'AI'"""))
    # ### end Alembic commands ###

