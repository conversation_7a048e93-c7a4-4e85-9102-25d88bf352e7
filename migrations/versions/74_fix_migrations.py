"""fix inconsistencies between DB and Models

Revision ID: 1dd339249d4e
Revises: 624e622f3145
Create Date: 2025-05-05 15:27:00.609733

"""
from alembic import op


# revision identifiers, used by Alembic.
revision = '1dd339249d4e'
down_revision = '624e622f3145'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # Following ones we actually need to run as migrations, DB does not have any of those.
    # Other things have been cleaned up in the code itself
    with op.batch_alter_table('collaborations', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_collaborations_shared_by_id'), ['shared_by_id'], unique=False)

    with op.batch_alter_table('collection_results', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_collection_results_user_id'), ['user_id'], unique=False)

    with op.batch_alter_table('landscape_profiles', schema=None) as batch_op:
        batch_op.drop_constraint('fk_landscape_competitive_profile_id', type_='foreignkey')
        batch_op.create_foreign_key('fk_landscape_competitive_profile_id', 'landscape_profiles', ['competitive_profile_id'], ['id'], ondelete='SET NULL')


def downgrade_():
    with op.batch_alter_table('landscape_profiles', schema=None) as batch_op:
        batch_op.drop_constraint('fk_landscape_competitive_profile_id', type_='foreignkey')
        batch_op.create_foreign_key('fk_landscape_competitive_profile_id', 'landscape_profiles', ['competitive_profile_id'], ['id'])

    with op.batch_alter_table('collection_results', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_collection_results_user_id'))

    with op.batch_alter_table('collaborations', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_collaborations_shared_by_id'))


def upgrade_auth():
    pass


def downgrade_auth():
    pass

