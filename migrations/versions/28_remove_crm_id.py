"""Remove crm id column from app_users table

Revision ID: 720df80126bd
Revises: e4627ddf1e16
Create Date: 2024-03-21 09:05:40.342040

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '720df80126bd'
down_revision = 'e4627ddf1e16'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    op.drop_column('app_users', 'crm_id')


def downgrade_auth():
    op.add_column('app_users', sa.Column('crm_id', sa.String(length=256), nullable=True))
