"""Add tag recommendation tables

Revision ID: c26ce893b8d1
Revises: d6bba7b1c8d9
Create Date: 2024-07-05 10:27:53.917754

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'c26ce893b8d1'
down_revision = 'd6bba7b1c8d9'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tag_recommendations',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('tag_id', sa.Integer(), nullable=False),
    sa.Column('score', sa.Float(), nullable=False),
    sa.Column('document_id', sa.Integer(), nullable=True),
    sa.Column('collection_result_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.Enum('ACCEPTED', 'REJECTED', 'PENDING', name='recommendationstatus'), nullable=False),
    sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['collection_result_id'], ['collection_results.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('tag_recommendations', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_tag_recommendations_document_id'), ['document_id'], unique=False)
        batch_op.create_index('ix_tag_recommendations_tag_id_status', ['tag_id', 'status'], unique=False)


def downgrade_():
    op.drop_table('tag_recommendations')


def upgrade_auth():
    pass

def downgrade_auth():
    pass
