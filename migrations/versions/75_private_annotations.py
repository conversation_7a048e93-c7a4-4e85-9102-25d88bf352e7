"""Private annotations

Revision ID: 7b4b0ae25009
Revises: 1dd339249d4e
Create Date: 2025-05-05 20:20:22.186171

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7b4b0ae25009'
down_revision = '1dd339249d4e'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()





def upgrade_():
    with op.batch_alter_table('documents_comments', schema=None) as batch_op:
        batch_op.add_column(sa.Column('private', sa.Boolean(), nullable=False, server_default='0'))

    with op.batch_alter_table('documents_labels', schema=None) as batch_op:
        batch_op.add_column(sa.Column('private', sa.Boolean(), nullable=False, server_default='0'))


def downgrade_():
    with op.batch_alter_table('documents_labels', schema=None) as batch_op:
        batch_op.drop_column('private')

    with op.batch_alter_table('documents_comments', schema=None) as batch_op:
        batch_op.drop_column('private')


def upgrade_auth():
    pass


def downgrade_auth():
    pass
