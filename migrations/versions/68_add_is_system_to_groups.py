"""Add is_system to groups

Revision ID: 4e63270608ec
Revises: fb3cab14bc7f
Create Date: 2025-03-24 09:47:11.324193

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = '743e29608ce3'
down_revision = '4fcb530fda3f'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_auth():
    op.add_column('groups', sa.Column('is_system', sa.Boolean(), nullable=False, server_default='0'))

    # Get all companies
    companies = op.get_bind().execute(text("SELECT id FROM companies")).fetchall()
    
    # Create Everyone group for each company
    for company in companies:
       company_id = company[0]
        
       # Create Everyone group
       op.get_bind().execute(
           text("""
              INSERT INTO groups (name, description, company_id, created_at, updated_at, is_system)
              VALUES ('Everyone', 'System group containing all users in the company', :company_id, NOW(), NOW(), 1)
              """),
              {"company_id": company_id}
       )
            
       # Get the newly created group ID
       group_id = op.get_bind().execute(
           text("SELECT id FROM groups WHERE company_id = :company_id AND name = 'Everyone'"),
           {"company_id": company_id}
       ).fetchone()[0]
       
       # Add all users from the company to the Everyone group
       op.get_bind().execute(
           text("""
              INSERT INTO users_groups (user_id, group_id)
              SELECT u.id, :group_id
              FROM users u
              WHERE u.company_id = :company_id
              AND NOT EXISTS (
                     SELECT 1 FROM users_groups ug
                     WHERE ug.user_id = u.id AND ug.group_id = :group_id
              )
              """),
              {"company_id": company_id, "group_id": group_id}
       )


def upgrade_():
    # No changes needed for empty database
    pass


def downgrade_auth():
     # Remove Everyone groups
    op.get_bind().execute(text("DELETE FROM users_groups WHERE group_id IN (SELECT id FROM groups WHERE name = 'Everyone')"))
    op.get_bind().execute(text("DELETE FROM groups WHERE name = 'Everyone'"))
    # Remove is_system column from groups table
    op.drop_column('groups', 'is_system')


def downgrade_():
    # No changes needed for empty database
    pass
