"""Additional params for search history

Revision ID: 12e1e4991c74
Revises: 78c613a0baa9
Create Date: 2023-04-21 12:37:30.870613

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '12e1e4991c74'
down_revision = 'e1718a1422f9'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.add_column('search_history', sa.Column('additional_params', sa.JSON(), nullable=True))


def downgrade_():
    op.drop_column('search_history', 'additional_params')


def upgrade_auth():
    pass


def downgrade_auth():
    pass


