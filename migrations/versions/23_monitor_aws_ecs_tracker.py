"""empty message

Revision ID: 402401bf0286
Revises: 06fdda999c8b
Create Date: 2024-01-22 15:51:31.261050

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '402401bf0286'
down_revision = '0225089ef133'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.create_table(
        'monitor_aws_ecs_tracker',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('profile_id', sa.Integer(), nullable=False),
        sa.Column('run_id', sa.Integer(), nullable=True),
        sa.Column('task_arn', sa.String(length=100), nullable=True),
        sa.Column('status', sa.String(length=10), nullable=False),
        sa.Column('prediction_output_key', sa.String(length=100), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade_():
    op.drop_table('monitor_aws_ecs_tracker')


def upgrade_auth():
    pass


def downgrade_auth():
    pass
