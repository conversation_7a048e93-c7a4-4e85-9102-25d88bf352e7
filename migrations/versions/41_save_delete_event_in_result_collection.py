"""Save delete event in result collection

Revision ID: cf2ac1af503e
Revises: 9236a374a7de
Create Date: 2024-10-15 12:29:16.364277

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'cf2ac1af503e'
down_revision = '9236a374a7de'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()





def upgrade_():
    op.add_column('collection_source', sa.Column('documents_count', sa.Integer(), nullable=True))
    op.add_column('collection_source', sa.Column('is_remove_event', sa.<PERSON>(), nullable=True))
    op.execute('UPDATE collection_source SET is_remove_event = 0')
    op.execute('UPDATE collection_source SET documents_count = 0')

    op.execute(sa.text("""UPDATE collection_source SET documents_count = 
                       (select COUNT(collection_result_source.id) from collection_result_source
                        where collection_result_source.collection_source_id = collection_source.id)"""))

    op.alter_column('collection_source', 'documents_count', existing_type=sa.Integer(), nullable=False)
    op.alter_column('collection_source', 'is_remove_event', existing_type=sa.Boolean(), nullable=False)


def downgrade_():
    op.drop_column('collection_source', 'documents_count')
    op.drop_column('collection_source', 'is_remove_event')


def upgrade_auth():
    pass


def downgrade_auth():
    pass

