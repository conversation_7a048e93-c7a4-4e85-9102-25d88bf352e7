"""private tags

Revision ID: fb3cab14bc7f
Revises: dd005255ded9
Create Date: 2025-03-18 11:32:04.748472

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'fb3cab14bc7f'
down_revision = 'b809fad1d161'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.add_column('tags', sa.Column('private', sa.Boolean(), nullable=True, default=False))
    op.execute('UPDATE tags t SET t.private = 0')
    op.alter_column('tags', 'private', existing_type=sa.Boolean(), nullable=False)

def downgrade_():
    op.drop_column('tags', 'private')


def upgrade_auth():
    pass


def downgrade_auth():
    pass

