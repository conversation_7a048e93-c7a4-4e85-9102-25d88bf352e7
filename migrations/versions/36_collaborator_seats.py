"""Add collaborator seats

Revision ID: ab7df0878378
Revises: 86f2bd132caf
Create Date: 2024-08-26 14:06:52.870482

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'ab7df0878378'
down_revision = '86f2bd132caf'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    op.add_column('companies', sa.Column('collaborator_seats', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))


def downgrade_auth():
    op.drop_column('companies', 'collaborator_seats')
