"""Migrate collection source results

Revision ID: e93a2f31c098
Revises: ab7df0878378
Create Date: 2024-08-29 10:38:06.089200

"""
from alembic import op
from sqlalchemy import text
# revision identifiers, used by Alembic.
revision = 'e93a2f31c098'
down_revision = 'ab7df0878378'
branch_labels = None
depends_on = None



def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.execute(text("""
        insert into collection_result_source (created_at, updated_at, score, collection_source_id, collection_result_id)
        select cr.created_at, cr.updated_at, NULL, cs.id, cr.id 
        from collection_results cr 
        join collection_source cs on cr.collection_id = cs.collection_id 
        left outer join collection_result_source crs on cr.id = crs.collection_result_id and cs.id = crs.collection_source_id 
        where crs.collection_result_id is NULL 
        and cr.collection_id in (select cs.collection_id from collection_source cs group by cs.collection_id having count(cs.id) = 1)
    """))


def downgrade_():
    pass


def upgrade_auth():
    pass


def downgrade_auth():
    pass
