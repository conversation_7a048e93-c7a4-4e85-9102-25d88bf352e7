"""Support group assignments for tasks

Revision ID: 26e553f1b27a
Revises: 9236a374a7de
Create Date: 2024-10-16 16:56:38.870288

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '26e553f1b27a'
down_revision = 'cf2ac1af503e'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.add_column('task_assignment', sa.Column('assignee_type', sa.Enum('USER', 'GROUP', name='assigneetype'), nullable=True))
    op.add_column('task_assignment', sa.Column('answered_by', sa.Integer(), nullable=True))
    op.execute("UPDATE task_assignment SET assignee_type = 'USER', answered_by = assignee_id")
    op.alter_column('task_assignment', 'assignee_type', existing_type=sa.Enum('USER', 'GROUP', name='assigneetype'), nullable=False)


def downgrade_():
    op.drop_column('task_assignment', 'assignee_type')


def upgrade_auth():
    pass


def downgrade_auth():
    pass
