"""Remove rating feature flag


Revision ID: 7b529e3726e3
Revises: 62820ef1ef31
Create Date: 2025-01-13 10:59:35.712914

"""
from alembic import op
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = '7b529e3726e3'
down_revision = '16b21f039729'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(text("""delete from user_features where feature_id in 
                    (select id from features where short_name = 'R')"""))
    op.execute(text("""delete from company_features where feature_id in 
                    (select id from features where short_name = 'R')"""))
    op.execute(text("""delete from features where short_name = 'R'"""))
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""INSERT INTO features (short_name, name) VALUES ('R', 'Rating')""")
    # ### end Alembic commands ###
