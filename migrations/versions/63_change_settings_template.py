"""empty message

Revision ID: b809fad1d161
Revises: dd005255ded9
Create Date: 2025-03-17 16:23:55.298034

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'b809fad1d161'
down_revision = 'dd005255ded9'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('settings_templates_features')
    with op.batch_alter_table('settings_templates', schema=None) as batch_op:
        batch_op.add_column(sa.Column('company_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('save_history', sa.<PERSON>(), nullable=False, default=True))
        batch_op.create_foreign_key(None, 'companies', ['company_id'], ['id'])
        batch_op.drop_column('domain')
        batch_op.drop_column('subscription_type')
        batch_op.drop_column('name')


def downgrade_auth():
    pass
