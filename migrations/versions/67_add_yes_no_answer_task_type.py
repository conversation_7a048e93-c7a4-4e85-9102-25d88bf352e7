"""Add YES_NO_ANSWER task type

Revision ID: 4fcb530fda3f
Revises: 225faeba11ad
Create Date: 2025-03-18 16:54:24.193649

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = '4fcb530fda3f'
down_revision = '225faeba11ad'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'task',
        'task_type',
        existing_type=sa.Enum('STAR_RATING', name='taskanswertypeenum'),
        type_=sa.Enum('STAR_RATING', 'YES_NO_ANSWER', name='taskanswertypeenum'),
        existing_nullable=False
    )
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(text('''
               UPDATE task 
               SET task_type = 'STAR_RATING'
               WHERE task_type = 'YES_NO_ANSWER'
           '''))
    op.alter_column(
        'task',
        'task_type',
        existing_type=sa.Enum('STAR_RATING', 'YES_NO_ANSWER', name='taskanswertypeenum'),
        type_=sa.Enum('STAR_RATING', name='taskanswertypeenum'),
        existing_nullable=False
    )
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""INSERT INTO features (short_name, name) VALUES ('BR', 'Binary rating')""")
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(text("""DELETE FROM features WHERE short_name = 'BR'"""))
    # ### end Alembic commands ###
