"""New column shared_by_id in collaboration table

Revision ID: 83cdcf8e48c0
Revises: 8c8d75532052
Create Date: 2024-11-12 07:59:21.241100

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '83cdcf8e48c0'
down_revision = '8c8d75532052'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.add_column('collaborations', sa.Column('shared_by_id', sa.Integer(), nullable=True))
    op.execute('UPDATE collaborations SET collaborations.shared_by_id = collaborations.owner_id')
    op.alter_column('collaborations', 'shared_by_id', existing_type=sa.Integer(), nullable=False)


def downgrade_():
    op.drop_column('collaborations', 'shared_by_id')


def upgrade_auth():
    pass


def downgrade_auth():
    pass
