services:
  redis:
    image: redis:7.0.12-alpine
    command: redis-server --port 6389 --requirepass dhdJD232sHSJAHDKIS

  mysql:
    image: mariadb:10.5.3-focal
    command: mysqld --sql_mode="NO_ENGINE_SUBSTITUTION" --character-set-server=utf8mb4 --collation-server=utf8mb4_general_ci
    environment:
      MYSQL_ROOT_PASSWORD: 62aHSjdKaJs728SjalsJ
      MYSQL_DATABASE: octimine_auth
      MYSQL_USER: cherry
      MYSQL_PASSWORD: ASDFNJxn4583fdsDHSKns736
    volumes:
      - ./init_db.sql:/docker-entrypoint-initdb.d/init.sql

  rabbitmq:
    image: rabbitmq:4.1.1-management-alpine
    environment:
      RABBITMQ_DEFAULT_USER: engine-dev
      RABBITMQ_DEFAULT_PASS: 2EGtEjecEzqaE857

  elasticsearch:
    image: registry.gitlab.com/octimine/shared_registry/elasticsearch-testdata:8.12.0
    environment:
      discovery.type: single-node

  neo4j:
    image: registry.gitlab.com/octimine/shared_registry/neo4j-testdata
    environment:
      NEO4J_AUTH: neo4j/test

  mock-mysql:
    image: registry.gitlab.com/octimine/shared_registry/mock-mysql:latest
    command: mysqld --max_allowed_packet=64M --log_error=/var/log/mysql/general-log.log --character-set-server=utf8mb4 --collation-server=utf8mb4_general_ci --default-time-zone='+02:00'
    environment:
      MYSQL_ROOT_PASSWORD: NO^m3xUj
      MYSQL_DATABASE: laboratory
      MYSQL_USER: engine-dev
      MYSQL_PASSWORD: NO^m3xUj

  mock-search-engine:
    image: registry.gitlab.com/octimine/shared_registry/mock-search-engine:latest
    depends_on:
      - mock-mysql
      - rabbitmq

  swagger_ui:
    image: nginx
    volumes:
      - ./docs:/usr/share/nginx/html

networks:
  default:
    ipam:
      driver: default
      config:
        - subnet: ***********/24
