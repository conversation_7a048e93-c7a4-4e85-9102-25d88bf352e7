import random
import re
import time
import uuid
from datetime import datetime
from urllib.parse import quote

import pytest
import requests
from http import HTTPStatus
from flask_jwt_extended import create_access_token, get_jti
from app.app import session_store
from tests.integration_tests.base_integration_test import family_id, family_ids, TestIntegration

from tests.integration_tests.constants import API_BASE_URL
from tests.unit_tests.web_api import data, url
from tests.unit_tests.web_api.test_subsample import random_word


class TestIntegrationSearch(TestIntegration):
    patent_documents_ids = [59662569, 52693395, 13963964]
    patent_numbers = ["US-20090094134-A1", "AU-2012100567-A4", "WO-2015127225-A1"]
    patent_numbers_normalized = ["US20090094134A1", "AU2012100567A4", "WO2015127225A1"]

    @pytest.fixture(scope='class')
    def search_access_token(self, app):
        with app.app_context():
            return self._create_search_access_token(
                identity="Integration Test", user_claims={"user_id": 1, "st": "PROFESSIONAL", "sh": True}
            )

    @pytest.fixture(autouse=True)
    def search_authenticate(self, search_access_token):
        self.token = search_access_token

    @classmethod
    def get_npl_query(cls):
        cache_breaker = cls.break_cache()
        return {
            "search_input": "wireless communications " + cache_breaker,
        }

    @classmethod
    def get_citation_query(cls, level=1):
        cache_breaker = cls.break_cache()
        return {
            "patent_numbers": [
                "GB-0027280-D0",
                "EP-0926872-A2",
                "JP-H10188004-A",
                "WO2018006959A1",
                "DE-%s-A1" % cache_breaker
            ],
            "direction": "bidirectional",
            "level": level
        }

    @classmethod
    def _create_search_access_token(cls, identity, user_claims):
        access_token = create_access_token(identity=identity, additional_claims={'user_claims': user_claims})
        session_store.set(get_jti(access_token), "false", 300)
        return access_token

    @classmethod
    def generate_share_code(cls):
        return str(uuid.uuid4())

    def search_send_request(self, url, request_dict=None, method='post', expected_status=HTTPStatus.OK, headers=None):
        server_response = self.search_get_server_response(url, request_dict, method, headers=headers)
        assert server_response.status_code == expected_status.value, \
            f"Unexpected server response: {server_response.status_code}, {server_response.content}"
        return server_response.json() if server_response.headers['Content-Type'] == 'application/json' else dict()

    def search_get_server_response(self, url, json_dict, method, allow_redirects=True, headers=None):
        if not url.startswith(API_BASE_URL):
            url = API_BASE_URL + url
        headers = headers or {}
        if 'Authorization' not in headers:
            headers['Authorization'] = "Bearer " + str(self.token)
        return requests.request(method, url, json=json_dict, headers=headers, allow_redirects=allow_redirects)

    @classmethod
    def get_similarity_query(cls):
        return {
            "source_document_ids": [48325351],
            "target_document_ids": [38478971, 48428361, 37663071] + [random.randint(1, 1000000000) * -1]
        }

    @classmethod
    def get_patent_number_query(cls):
        cache_breaker = cls.break_cache()
        return {
            "patent_numbers": [*cls.patent_numbers, "DE-%s-A1" % cache_breaker]
        }

    def _list_boolean_templates(self):
        return self.send_request("/web/boolean_templates", "", "get", HTTPStatus.OK)

    def _get_boolean_template(self, boolean_template_id, expected_status):
        res = self.send_request(f"/web/boolean_templates/{boolean_template_id}", "", "get", expected_status)
        return res["data"] if expected_status == HTTPStatus.OK else None

    def _create_boolean_template(self, title, content, query_type):
        token_author = self._create_access_token(company_id=1)
        payload = {"title": title, "content": content, 'type': query_type}
        res = self.send_request("/web/boolean_templates", payload, "post", HTTPStatus.CREATED, token_author)
        return res["data"]

    def _update_boolean_template(self, boolean_template_id, title, content, query_type):
        payload = {"title": title, "content": content, 'type': query_type}
        response = self.send_request(f"/web/boolean_templates/{boolean_template_id}", payload, "patch", HTTPStatus.OK)
        return response["data"]

    def _delete_boolean_template(self, boolean_template_id):
        self.send_request(f"/web/boolean_templates/{boolean_template_id}", "", "delete", HTTPStatus.NO_CONTENT)

    @classmethod
    def get_family_id_query(cls):
        return {
            "documents_ids": cls.patent_documents_ids + [random.randint(1, 1000000000) * -1]
        }

    @classmethod
    def get_publication_id_query(cls):
        cache_breaker = cls.break_cache()
        return {
            "publication_numbers": [*cls.patent_numbers, "DE-%s-A1" % cache_breaker]
        }

    def test_documentation(self):
        self.search_send_request('/search/docs', '', 'get', HTTPStatus.OK)

    def test_boolean_search(self):
        request = self.get_boolean_query()
        response = self.search_send_request('/search/boolean?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post')
        assert len(response["data"]["documents"]) == 6
        response = self.search_send_request('/search/boolean?show_analytics=1&show_general=1&show_bibliographic=1'
                                            '&sort_by=recency&sort_order=desc', request, 'post', HTTPStatus.OK)
        assert len(response["data"]["documents"]) == 6

        request['search_input'] = "(TITLE=" + self.break_cache() + " OR TEXT=test \"process\"~2)"
        response = self.search_send_request('/search/boolean?show_analytics=1&show_general=1&show_bibliographic=1'
                                            '&sort_by=recency&sort_order=desc', request, 'post', HTTPStatus.OK)

    def test_boolean_search_response(self):
        response = self._perform_boolean_search()
        assert len(response["data"]["documents"])

    def test_boolean_publication_search(self):
        request = {
            "search_input": "PUBLICATION_DATE>2022-06-01 OR TITLE=" + self.break_cache(),
            "search_type": "PUBLICATION"
        }
        response = self.search_send_request('/search/boolean?&show_general=1&show_bibliographic=1', request, 'post')
        assert len(response["data"]["publications"]) == 13

    def test_boolean_search_without_caching_results(self):
        request = self.get_boolean_query()
        request['cache'] = False
        response = self.search_send_request('/search/boolean', request, 'post')
        assert response["data"]["search_info"]['new_search']
        response = self.search_send_request('/search/boolean', request, 'post')
        assert response["data"]["search_info"]['new_search']

    def test_semantic_search(self):
        request = self.get_semantic_query()
        response = self.search_send_request('/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post')
        assert len(response["data"]["documents"]) == 25
        response = self.search_send_request('/search/semantic?show_analytics=1&show_general=0&page=2',
                                            request, 'post')
        assert len(response["data"]["documents"]) == 25

    def test_semantic_search_response(self):
        response = self._perform_semantic_search()
        assert len(response["data"]["documents"])

    def test_semantic_search_without_caching_results(self):
        request = self.get_semantic_query()
        request['cache'] = False

        response = self.search_send_request('/search/semantic', request, 'post')
        assert response["data"]["search_info"]['new_search']
        response = self.search_send_request('/search/semantic', request, 'post')
        assert response["data"]["search_info"]['new_search']

    def test_semantic_search_with_relevant_search_hash(self):
        request = self.get_boolean_query()
        response = self.search_send_request('/search/boolean?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post')
        relevant_search_hash = response['data']['search_info']['search_hash']
        boolean_hits = response['data']['page']['total_hits']
        request = self.get_semantic_query()
        request['search_filters']['relevant_search_hash'] = relevant_search_hash
        response = self.search_send_request('/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post')
        assert len(response["data"]["documents"]) == boolean_hits

    def test_semantic_search_with_relevant_docdb_family_ids(self):
        request = self.get_semantic_query()
        del request['docdb_family_ids']
        request['search_filters'] = {'relevant_docdb_family_ids': [3831980, 4160912, 4576786, 4907403, 5577909]}
        response = self.search_send_request('/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post')
        assert len(response['data']['documents']) == 5

    def test_semantic_search_with_publication_numbers(self):
        def get_hit_ids(res):
            return [d['general']['docdb_family_id'] for d in res['data']['documents']]

        request = self.get_semantic_query()
        response = self.search_send_request('/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post')
        hit_ids = get_hit_ids(response)
        del request['docdb_family_ids']
        request['patent_numbers'] = ['US-20100107229-A1', 'EP-3502942-A1', 'US-20060219776-A1',
                                     'EP-1705883-A1', 'US-9338137-B1']
        response = self.search_send_request('/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post')
        assert hit_ids == get_hit_ids(response)

    def test_semantic_search_with_application_numbers(self):
        request = self.get_semantic_query()
        del request['docdb_family_ids']
        request['patent_numbers'] = ['JP-11028498-A']
        response = self.search_send_request('/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post')
        assert len(response["data"]["documents"]) == 25
        response = self.search_send_request('/search/semantic?show_analytics=1&show_general=0&page=2',
                                            request, 'post')
        assert len(response["data"]["documents"]) == 25

    def test_semantic_with_boost_factors(self):
        request = self.get_semantic_query()
        request['boost_factors'] = {
            'keywords': [
                {'term': 'wireless', 'boost': 1.5}
            ],
            'cpc_codes': [
                {'term': 'G06Q20/322', 'boost': 2.0}
            ],
            'ipc_codes': [
                {'term': 'A47G29/14', 'boost': 0.5}
            ]
        }
        self.search_send_request('/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1', request,
                                 'post')

    def test_semantic_with_boost_factors_ok(self):
        request = self.get_semantic_query()
        request['boost_factors'] = {
            'keywords': [
                {'term': 'wireless', 'boost': 1.5}
            ],
            'cpc_codes': [
                {'term': 'G06Q20/322', 'boost': 2.0}
            ],
            'ipc_codes': [
                {'term': 'A47G29/14', 'boost': 0.5}
            ]
        }
        self.search_send_request('/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1',
                                 request, 'post',
                                 HTTPStatus.OK)

    def test_semantic_search_filters_1(self):
        request = self.get_semantic_query()
        request['search_filters'].update({
            "earliest_publication_date": "2000-01-01",
            "latest_priority_date": "2012-01-01",
            "free_text_query": "TECH_FIELDS=(Medical Technology OR Digital Communication)",
            "ipc_minus": ["H04N7/18"]
        })
        response = self.search_send_request('/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1'
                                            '&show_fulltext=1', request, 'post')
        assert len(response["data"]["documents"]) == 25

    def test_semantic_search_filters_2(self):
        request = self.get_semantic_query()
        request['search_filters'].update({
            "top_applicants": 15,
            "min_applicant_count": 1,
            "similarity_index": 800,
        })
        response = self.search_send_request('/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request,
                                            'post')
        assert len(response["data"]["documents"]) == 1

    def test_npl_search(self):
        request = self.get_npl_query()
        response = self.search_send_request('/search/npl_v2', request, 'post')
        assert len(response["data"]["papers"]) == 18

    def test_npl_search_response(self):
        response = self._perform_npl_search()
        assert len(response["data"]["papers"])

    def test_npl_search_with_filters(self):
        request = self.get_npl_query()
        request['search_filters'] = {
            'author': 'S',
            'min_year': 2000,
            'max_year': 2018,
            'venue': 'physical review',
            'open_access': True,
        }
        response = self.search_send_request('/search/npl_v2', request, 'post')
        assert len(response["data"]["papers"]) == 1

    def test_npl_search_without_caching_results(self):
        request = self.get_npl_query()
        request['cache'] = False

        response = self.search_send_request('/search/npl_v2', request, 'post')
        assert response["data"]["search_info"]['new_search']
        response = self.search_send_request('/search/npl_v2', request, 'post')
        assert response["data"]["search_info"]['new_search']

    def test_citation_search_flat(self):
        request = self.get_citation_query(level=1)
        response = self.search_send_request('/search/citation_flat?show_citations=0&show_general=1&show_analytics=1'
                                            '&sort_by=publication_authority&page=2', request, 'post')

        assert response["data"]["page"]["total_hits"] == 446
        assert len(response["data"]["documents"]) == 25
        response = self.search_send_request('/search/citation_flat?show_citations=1&include_focal=1&page_size=50',
                                            request,
                                            'post')
        assert len(response["data"]["documents"]) == 50
        assert sum([len(d["citations"]) for d in response["data"]["documents"]]) == 197
        # Only NPL nodes from citation search
        hash_response = self.search_send_request('/search/node/' + response["data"]["search_info"]["search_hash"],
                                                 None, 'get')

        assert len(hash_response["data"]["nodes"]) == 3
        assert hash_response["data"]["page"]["total_hits"] == 3

    def test_citation_search_without_caching_results(self):
        request = self.get_citation_query()
        request['cache'] = False
        response = self.search_send_request('/search/citation_flat', request, 'post')
        assert response["data"]["search_info"]['new_search']
        response = self.search_send_request('/search/citation_flat', request, 'post')
        assert response["data"]["search_info"]['new_search']

    def test_citation_search_by_docdb_family_id_flat(self):
        request = {
            "docdb_family_ids": [9902787, 25538787, 18410662, 56511552, random.randint(1, 100000) * -1],
            "direction": "bidirectional",
            "level": 1
        }
        response = self.search_send_request('/search/citation_flat', request, 'post')
        assert response["data"]["page"]["total_hits"] == 446

    def test_citation_search_by_application_number(self):
        request = self.get_citation_query(level=1)
        request['patent_numbers'].append('JP-11028498-A')
        response = self.search_send_request('/search/citation_flat', request, 'post')
        assert len(response['data']['documents'])

    def test_citation_publication_search_flat(self):
        request = self.get_citation_query(level=1)
        request['search_type'] = 'PUBLICATION'
        response = self.search_send_request(
            '/search/citation_flat?include_focal=0&show_general=1&show_bibliographic=1', request, 'post'
        )

        assert response["data"]["page"]["total_hits"]
        assert response["data"]["publications"]

    def test_citation_publication_search_by_docdb_family_id_flat(self):
        request = {
            "docdb_family_ids": [9902787],
            "direction": "bidirectional",
            "search_type": "PUBLICATION",
            "level": 1,
            "cache": False
        }
        response = self.search_send_request(
            '/search/citation_flat?include_focal=0&show_general=1&show_bibliographic=1', request, 'post'
        )
        assert response["data"]["page"]["total_hits"]
        assert response["data"]["publications"]

    def test_citation_search_filters(self):
        request = self.get_citation_query(level=2)
        request.update({
            "direction": "citation",
            "citation_phase": "SEA",
            "citation_category": "A",
            "document_type": "Publication",
            "search_filters": {
                "earliest_publication_date": "1900-01-01",
                "free_text_query": "AUTHORITIES=(DE OR FR)"
            }
        })
        response = self.search_send_request('/search/citation_flat?page_size=50', request, 'post')
        assert len(response["data"]["documents"]) == 1

    def test_patent_number_search_for_families(self):
        request = self.get_patent_number_query()
        request['patent_numbers'].append('JP-11028498-A')  # Application numbers should be supported also
        response = self.search_send_request('/search/patent_number?show_analytics=1&show_general=1&show_bibliographic='
                                            '1&show_fulltext=1', request, 'post')
        assert len(response["data"]["documents"]) == 4
        original_numbers = set([
            on for doc in response['data']['documents']
            for on in (doc['general']['original_numbers'] or [])
        ])
        assert len(original_numbers) == 4

    def test_patent_number_search_for_publications(self):
        request = self.get_patent_number_query()
        request['patent_numbers'].append('JP-11028498-A')
        request['search_type'] = 'PUBLICATION'
        response = self.search_send_request('/search/patent_number?show_analytics=1&show_general=1&show_bibliographic='
                                            '1&show_fulltext=1', request, 'post')
        assert len(response["data"]["publications"]) == 4
        original_numbers = set([
            on for doc in response['data']['publications']
            for on in (doc['general']['original_numbers'] or [])
        ])
        assert len(original_numbers) == 4

    def test_patent_number_search_without_caching_results(self):
        request = self.get_patent_number_query()
        request['cache'] = False
        response = self.search_send_request('/search/patent_number', request, 'post')
        assert response["data"]["search_info"]['new_search']
        response = self.search_send_request('/search/patent_number', request, 'post')
        assert response["data"]["search_info"]['new_search']

    def test_chunked_patent_number_search_merge(self):
        request = self.get_patent_number_query()
        request['patent_numbers'].extend([
            "US-655661-A",
            "NL-6615527-A",
            "AU-PR815201-A0"
        ])
        response = self.search_send_request('/search/patent_number?show_analytics=1&show_general=1&show_bibliographic='
                                            '1&show_fulltext=1', request, 'post')
        assert len(response['data']['documents']) == 6
        assert len(response['data']['documents'][1]['general']['original_numbers']) == 1

    def test_family_id_search(self):
        request = {"documents_ids": self.patent_documents_ids}
        response = self.search_send_request('/search/document?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post', HTTPStatus.OK)
        assert len(response["data"]["documents"]) == 2

    def test_family_id_search_without_caching_results(self):
        request = {"documents_ids": self.patent_documents_ids, 'cache': False}
        response = self.search_send_request('/search/document', request, 'post', HTTPStatus.OK)
        assert response["data"]["search_info"]['new_search']
        response = self.search_send_request('/search/document', request, 'post', HTTPStatus.OK)
        assert response["data"]["search_info"]['new_search']

    def test_publication_id_search(self):
        request = {"publication_numbers": self.patent_numbers}
        response = self.search_send_request('/search/document?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post', HTTPStatus.OK)
        assert len(response["data"]["publications"]) == 3

    def test_publication_id_search_without_caching_results(self):
        request = {"publication_numbers": self.patent_numbers, 'cache': False}
        response = self.search_send_request('/search/document', request, 'post', HTTPStatus.OK)
        assert response["data"]["search_info"]['new_search']
        response = self.search_send_request('/search/document', request, 'post', HTTPStatus.OK)
        assert response["data"]["search_info"]['new_search']

    def test_publication_id_search_with_normalized_publication_numbers(self):
        request = {"publication_numbers": self.patent_numbers_normalized}
        response = self.search_send_request('/search/document?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post', HTTPStatus.OK)
        assert len(response["data"]["publications"]) == 3

    def test_family_id_search_with_additional_info(self):
        request = {
            "documents_ids": self.patent_documents_ids,
            "additional_info": {
                "similarity_index": [i for i, _ in enumerate(self.patent_documents_ids)]
            }
        }

        response = self.search_send_request('/search/document?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post', HTTPStatus.OK)
        assert len(response["data"]["documents"]) == 2
        assert "similarity_index" in response["data"]["documents"][0]["general"]

    def test_family_id_search_with_filters(self):
        request = {
            "documents_ids": self.patent_documents_ids,
            "search_filters": {
                "free_text_query": "TECH_AREAS=Electrical Engineering OR PUBLICATION_KIND=A"
            }
        }
        response = self.search_send_request('/search/document?sort_by=publication_date', request, 'post', HTTPStatus.OK)
        assert len(response["data"]["documents"]) == 2

        request["search_filters"]["free_text_query"] = "TECH_AREAS=Electrical Engineering AND IPC4=H02M"
        response = self.search_send_request('/search/document?sort_by=publication_date', request, 'post', HTTPStatus.OK)
        assert len(response["data"]["documents"]) == 1

    def test_family_by_id(self):
        response = self.search_send_request('/search/document/' + str(self.patent_documents_ids[0]), None, 'get')
        assert response["data"]["document"]

    def test_publication_by_id(self):
        response = self.search_send_request('/search/publications/' + str(self.patent_numbers[0]), None, 'get')
        assert response["data"]["publication"]

    def test_preprocessed_text(self):
        request = {'documents_ids': self.patent_documents_ids}
        response = self.search_send_request('/search/preprocessed', request, 'post', HTTPStatus.OK)
        assert response["data"]["preprocessed_text"]

    def test_term(self):
        # This test does not have a cache breaker so after a second run, the execution of the endpoint may take
        # the 'cache route'
        response = self.search_send_request('/search/term?field=applicants&value=siem',
                                            None, 'get')
        assert len(response["data"]["results"]) == 7
        # Test getting term from search results
        response = self.search_send_request('/search/boolean', self.get_boolean_query(), 'post')
        search_hash = response["data"]["search_info"]["search_hash"]
        response = self.search_send_request('/search/term?field=applicants&search_hash=' + search_hash,
                                            None, 'get')
        assert len(response["data"]["results"]) == 6

    def test_get_search_by_hash(self):
        # Semantic
        semantic_query = self.get_semantic_query()
        semantic_response = self.search_send_request('/search/semantic', semantic_query, 'post')
        semantic_search_hash = semantic_response['data']['search_info']['search_hash']
        hash_response = self.search_send_request('/search/hash/' + semantic_search_hash,
                                                 {'search_filters': semantic_query['search_filters']}, 'post')

        assert semantic_response["data"]["page"]["total_hits"] == hash_response["data"]["page"]["total_hits"]
        # Boolean
        boolean_response = self.search_send_request('/search/boolean', self.get_boolean_query(), 'post')
        boolean_search_hash = boolean_response['data']['search_info']['search_hash']
        hash_response = self.search_send_request('/search/hash/' + boolean_search_hash, None, 'get')
        assert boolean_response["data"]["page"]["total_hits"] == hash_response["data"]["page"]["total_hits"]
        # Citation
        citation_response = self.search_send_request('/search/citation_flat?include_focal=1',
                                                     self.get_citation_query(), 'post')
        citation_search_hash = citation_response['data']['search_info']['search_hash']
        hash_response = self.search_send_request('/search/hash/' + citation_search_hash, None, 'get')
        assert citation_response["data"]["page"]["total_hits"] == hash_response["data"]["page"]["total_hits"]
        # Document Id
        document_id_response = self.search_send_request('/search/document',
                                                        {'documents_ids': self.patent_documents_ids},
                                                        'post')
        document_id_search_hash = document_id_response['data']['search_info']['search_hash']
        hash_response = self.search_send_request('/search/hash/' + document_id_search_hash,
                                                 None, 'get')
        assert document_id_response["data"]["page"]["total_hits"] == hash_response["data"]["page"]["total_hits"]
        # Document Id with additional info
        document_id_response = self.search_send_request(
            '/search/document',
            {
                'documents_ids': self.patent_documents_ids,
                "additional_info": {
                    "similarity_index": [random.randint(0, 100) for _ in self.patent_documents_ids]
                }
            },
            'post'
        )
        document_id_search_hash = document_id_response['data']['search_info']['search_hash']
        hash_response = self.search_send_request('/search/hash/' + document_id_search_hash, None, 'get')
        assert document_id_response["data"]["page"]["total_hits"] == hash_response["data"]["page"]["total_hits"]
        assert "similarity_index" in document_id_response["data"]["documents"][0]["general"]
        # With filters
        filters = {
            "search_filters": {
                "cpc_minus": ["A013/01"],
                "earliest_priority_date": "2005-01-01",
                "quantity_cut_off": 200
            }
        }
        hash_response = self.search_send_request('/search/hash/' + semantic_search_hash, filters, 'post')
        assert hash_response["data"]["page"]["total_hits"] == 132

    def test_export_results(self):
        # Semantic, export to excel
        semantic_response = self.search_send_request('/search/semantic', self.get_semantic_query(), 'post')
        semantic_search_hash = semantic_response['data']['search_info']['search_hash']
        self.search_send_request('/search/export/' + semantic_search_hash + '?format=excel',
                                 {"patent_documents_ids": [37996214, 35311927, 9306480]}, 'post')
        # Boolean, export to CSV
        boolean_response = self.search_send_request('/search/boolean', self.get_boolean_query(), 'post')
        boolean_search_hash = boolean_response['data']['search_info']['search_hash']
        self.search_send_request('/search/export/' + boolean_search_hash + '?format=csv', {}, 'post')
        # Citation, export to excel
        citation_response = self.search_send_request('/search/citation_flat', self.get_citation_query(), 'post')
        citation_search_hash = citation_response['data']['search_info']['search_hash']
        self.search_send_request('/search/export/' + citation_search_hash + '?format=excel',
                                 {}, 'post')
        # Patent numbers
        number_response = self.search_send_request('/search/patent_number',
                                                   {"patent_numbers": self.patent_numbers}, 'post')
        number_search_hash = number_response['data']['search_info']['search_hash']

        # PDF, try all
        self.search_send_request('/search/export/' + semantic_search_hash + '?format=PDF',
                                 {"patent_documents_ids": [37996214, 35311927, 9306480]}, 'post')
        self.search_send_request('/search/export/' + boolean_search_hash + '?format=PDF',
                                 {"patent_documents_ids": [37996214, 35311927, 9306480]}, 'post')
        self.search_send_request('/search/export/' + citation_search_hash + '?format=PDF',
                                 {"patent_documents_ids": [19853448, 13963964, 9395504]}, 'post')
        self.search_send_request('/search/export/' + number_search_hash + '?format=PDF',
                                 {"patent_documents_ids": [13963964, 9395504]}, 'post')

        # Just ad-hoc documents for monitoring report
        self.search_send_request('/search/export?format=PDF',
                                 {"patent_documents_ids": [19853448, 13963964, 9395504],
                                  "title": "Monitoring report", "subtitle": "Blah"}, 'post')

        # export with custom fields
        response = self.search_get_server_response('/search/export/' + semantic_search_hash + '?format=CSV',
                                                   {"patent_documents_ids": [37996214, 35311927, 9306480],
                                                    "custom_fields": ["ipc", "title", "rank"]}, 'post')
        assert "IPC class,Title,Rank" in response.text
        assert "Technological fields" not in response.text
        response = self.search_get_server_response('/search/export/' + boolean_search_hash + '?format=CSV',
                                                   {"patent_documents_ids": [37996214, 35311927, 9306480],
                                                    "custom_fields": ["rank"]}, 'post')
        assert "Rank" in response.text
        assert "Title" not in response.text
        assert "Technological Fields" not in response.text
        response = self.search_get_server_response('/search/export/' + citation_search_hash + '?format=CSV',
                                                   {"patent_documents_ids": [19853448, 13963964, 9395504],
                                                    "custom_fields": ["ipc", "title"]}, 'post')
        assert "Rank" not in response.text
        assert "IPC class,Title" in response.text
        response = self.search_get_server_response('/search/export/' + number_search_hash + '?format=CSV',
                                                   {"patent_documents_ids": [13963964, 9395504],
                                                    "custom_fields": ["tech_fields", "rank"]}, 'post')
        assert "Rank" in response.text
        assert "Title" not in response.text
        assert "Technological fields,Rank" in response.text

        self.search_send_request('/search/export?format=PDF', {"patent_documents_ids": [19853448, 13963964, 9395504],
                                                               "title": "Monitoring report", "subtitle": "Blah",
                                                               "custom_fields": []}, 'post', HTTPStatus.BAD_REQUEST)

    def test_user_limitations(self):
        self.token = self._create_search_access_token(identity="Integration Test Free",
                                                      user_claims={
                                                          "user_id": 2, "is_admin": False,
                                                          "sq": 25, "rsq": 0, "at": 2, "mr": 5,
                                                          "st": "FREE"
                                                      })
        # Semantic
        response = self.search_send_request('/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1',
                                            self.get_semantic_query(), 'post')
        assert len(response["data"]["documents"]) == 5
        assert response["data"]["documents"][0]["general"].get("obfuscated")

        # Boolean
        response = self.search_send_request('/search/boolean?show_analytics=1&show_general=1&show_bibliographic=1',
                                            self.get_boolean_query(), 'post')
        assert len(response["data"]["documents"]) == 5  # Filters deactivated

    def test_statistics_search(self):
        response = self.search_send_request('/search/statistics', method='get')
        assert set(response.keys()) == {'number_of_patents', 'number_of_tech_fields', 'number_of_patent_families',
                                        'earliest_patent', 'last_update', 'average_also_published_as',
                                        'average_tech_fields', 'average_market_coverage',
                                        'average_impact', 'average_risk', 'average_recency_years',
                                        'average_citation_forward_count', 'average_consistency',
                                        'average_citation_backward_count'}

    def test_charts(self):
        boolean_response = self.search_send_request('/search/boolean',
                                                    {"search_input": "PRIORITY_DATE>1699-05-18"}, 'post')
        search_hash = boolean_response['data']['search_info']['search_hash']
        charts_to_generate = []  # Empty == all charts

        self.search_send_request('/search/charts/' + search_hash, {"charts": charts_to_generate}, 'post')
        self.search_send_request('/search/charts/' + search_hash, {
            "charts": charts_to_generate,
            "search_filters": {"free_text_query": "PUBLICATION_AUTHORITY=DE AND PUBLICATION_DATE<2015 "
                                                  "AND (CPC=A* OR IMPACT>2)"}}, 'post')
        self.search_send_request('/search/charts/' + search_hash, {
            "charts": charts_to_generate, "search_filters": {"free_text_query": "CLAIMS=Metal"}}, 'post')

    def test_async_family_id_search(self):
        request = self.get_family_id_query()
        self._test_async_search("/search/async/document", request, 2)

    def test_async_boolean_search(self):
        request = self.get_boolean_query()
        self._test_async_search("/search/async/boolean", request, 6)

    def test_async_boolean_publication_search(self):
        request = {
            "search_input": "PUBLICATION_DATE>2022-06-01 OR TITLE=" + self.break_cache(),
            "search_type": "PUBLICATION"
        }
        self._test_async_search("/search/async/boolean", request, 13, "publications")

    def test_async_patent_number_search(self):
        request = self.get_patent_number_query()
        request['patent_numbers'] = [pn.replace('-', '') for pn in request['patent_numbers']]
        response = self._test_async_search("/search/async/patent_number", request, 3)
        original_numbers = {on for d in response['data']['documents'] for on in d['general']['original_numbers']}
        assert len(original_numbers - set(request['patent_numbers'])) == 0

    def _test_async_search(self, url, request, expected_results, element_to_check="documents"):
        response = self.search_get_server_response(url, request, 'post', allow_redirects=False)
        assert response.status_code == HTTPStatus.ACCEPTED
        assert response.headers["Location"]
        status_url = response.headers["Location"]
        results_url = self._wait_until_async_search_finishes(status_url)
        response = self.search_send_request(results_url, None, 'get', HTTPStatus.OK)
        assert response["data"][element_to_check]
        assert len(response["data"][element_to_check]) == expected_results
        return response

    def _wait_until_async_search_finishes(self, status_url, max_wait_seconds=10):
        seconds_waited = 0
        while True:
            response = self.search_get_server_response(status_url, None, 'get', allow_redirects=False)
            if response.status_code == HTTPStatus.SEE_OTHER:
                assert response.headers["Location"]
                return response.headers["Location"]
            elif response.status_code == HTTPStatus.OK:
                response_body = response.json()
                assert response_body["status"] == "PENDING"
                assert response_body["search_hash"]
            else:
                assert False, f"Invalid response type for async status endpoint: {response.status_code}"

            if seconds_waited > max_wait_seconds:
                assert False, "Maximum seconds to wait for finished task reached"
            seconds_waited += 1
            time.sleep(1)

    def test_cache_expires_fast_for_high_search_quota_users(self, app):
        access_token = self._create_search_access_token(identity="User with big search quota",
                                                        user_claims={
                                                            "user_id": 3, "is_admin": False,
                                                            "sq": 100000000000, "at": 1000, "mr": 1000,
                                                            "st": "PROFESSIONAL"
                                                        })
        headers = {'Authorization': f"Bearer {access_token}"}
        response = self.search_send_request("/search/semantic", self.get_semantic_query(), 'post', headers=headers)
        assert response["data"]["search_info"]["search_hash"]
        search_hash = response["data"]["search_info"]["search_hash"]
        time.sleep(app.config['MIN_SEARCH_CACHE_TIMEOUT'].seconds)
        self.search_send_request(f"/search/hash/{search_hash}", None, 'get', HTTPStatus.GONE, headers=headers)

    def test_get_legal_statuses_for_family_members(self):
        self.search_send_request('/search/legal_status/38829626', None, 'get')

    def test_get_legal_events_for_patent_family(self):
        self.search_send_request('/search/legal_status/38829626/events', None, 'get')
        self.search_send_request('/search/legal_status/38829626/events?publication_number=AR062171A1&impact=POSITIVE'
                                 '&sort_by=effective_date&sort_order=desc', None, 'get')

    def test_get_document_info_for_family_member(self):
        self.search_send_request('/search/document_info/38829626', None, 'get')

    def test_search_document_info_for_publication_numbers(self):
        request = {'publication_numbers': self.patent_numbers, 'scope': 'publication'}
        publication_scope_response = self.search_send_request('/search/document_info', request, 'post', HTTPStatus.OK)

        request['scope'] = 'family'
        family_scope_response = self.search_send_request('/search/document_info', request, 'post', HTTPStatus.OK)
        assert (len(family_scope_response['data']['publications']) >
                len(publication_scope_response['data']['publications']))

    def _test_get_classification_entries(self, classification_type: str):
        base_url = f'/search/classification/{classification_type}'
        self.search_send_request(base_url, None, 'get', HTTPStatus.BAD_REQUEST)
        self.search_send_request(f'{base_url}?classification_symbol=A01', None, 'get')
        self.search_send_request(f'{base_url}?content=Agriculture', None, 'get')
        self.search_send_request(f'{base_url}?classification_symbol=A01&content=Tech', None,
                                 'get', HTTPStatus.BAD_REQUEST)

    def _test_get_classification_children(self, classification_type: str):
        url = f'/search/classification/{classification_type}/children?{classification_type}_id=1'
        self.search_send_request(url, None, 'get')

    def _test_get_classification_ancestors(self, classification_type: str):
        url = f'/search/classification/{classification_type}/ancestors?id=10&include_ancestor_siblings=' \
              f'1&include_children=1'
        self.search_send_request(url, None, 'get')

    def test_classification_cpc(self):
        self._test_get_classification_entries('cpc')
        self._test_get_classification_children('cpc')
        self._test_get_classification_ancestors('cpc')

    def test_classification_ipc(self):
        self._test_get_classification_entries('ipc')
        self._test_get_classification_children('ipc')
        self._test_get_classification_ancestors('ipc')

    def test_rich_neighborhood(self):
        response = self.search_get_server_response('/search/async/rich_neighborhood',
                                                   {'docdb_family_ids': self.patent_documents_ids},
                                                   'post', allow_redirects=False)
        assert response.status_code == HTTPStatus.ACCEPTED
        assert response.headers["Location"]
        status_url = response.headers["Location"]
        seconds_waited = 0
        while True:
            response = self.search_get_server_response(status_url, None, 'get', allow_redirects=False)
            if response.status_code != HTTPStatus.ACCEPTED:
                response_body = response.json()
                break
            if seconds_waited > 10:
                assert False, "Maximum seconds to wait for finished task reached"
            seconds_waited += 1
            time.sleep(1)
        assert response_body["data"]
        assert 'labels' in response_body["data"]
        assert 'source' in response_body["data"]
        assert 'threshold' in response_body["data"]
        assert sorted(response_body["data"]["labels"]) == sorted(
            ['patent_number', 'impact', 'neighbor_impact_simple_average', 'neighbor_impact_weighted_average']
        )
        assert len(response_body["data"]["source"]) == 2

    def test_PMA_application_number_mapping(self):
        body = {
            'application_numbers': ['2013-225490', '12400045-6', '16/363692', '00101033-9', '15/427862', 'EP16854144A',
                                    '2013-000336'],
            'application_country_code': ['JP', 'EP', 'US', 'EP', 'US', 'EP', 'JP'],
            'application_dates': ['2013-10-30', '2012-10-31', '2019-03-25', '2000-01-20', '2017-02-08', '2016-10-03',
                                  '2013-01-24'],
            'application_ip_origin': ['national', 'national', 'national', 'national', 'national', 'octimine',
                                      'national']
        }
        response = self.search_get_server_response('/search/application_mapping', body,
                                                   'post', allow_redirects=False)
        assert (response.json()['hits'] == 6)
        assert (response.json()['docdb_family_id'] == [52631747, 47623894, 68055449, 7896066, 65495866, 58488390, None])

    def test_document_highlight(self):
        boolean_request = self.get_boolean_query()
        response = self.search_send_request('/search/boolean?show_general=1', boolean_request, 'post')
        doc = response["data"]["documents"][0]
        request = {
            'search_hash': response['data']['search_info']['search_hash'],
            'document_id': doc['general']['docdb_family_id']
        }
        response = self.search_send_request('/search/boolean/highlights', request, 'post')
        assert 'highlight' in response['data']

    def test_publication_highlight(self):
        boolean_request = self.get_boolean_query(has_filters=False)
        boolean_request['search_type'] = 'PUBLICATION'
        response = self.search_send_request('/search/boolean?show_general=1', boolean_request, 'post')
        doc = response["data"]["publications"][0]
        request = {
            'search_hash': response['data']['search_info']['search_hash'],
            'publication_number': doc['general']['publication_number'],
        }
        response = self.search_send_request('/search/boolean/highlights', request, 'post')
        assert 'highlight' in response['data']

    def test_search_root_corporate_entities(self):
        body = {
            'name': 'Sunny',
            'only_root': True
        }
        response = self.search_send_request('/search/corporate_entities', body, 'post')
        assert 'corporate_entities' in response['data']

    def test_search_corporate_entities(self):
        body = {
            'name': 'America',
            'only_root': False
        }
        response = self.search_send_request('/search/corporate_entities', body, 'post')
        assert 'corporate_entities' in response['data']

    def test_get_corporate_entity_children(self):
        response = self.search_send_request('/search/corporate_entities/837263/children', '', 'get')
        assert 'children' in response['data']

    def test_semantic_search_query_expansion(self):
        # Create a token with the QE feature enabled
        self.token = self._create_search_access_token(
            identity="Integration Test for query expansion, user has short query (less than 20 words)",
            user_claims={"user_id": 4, "st": "PROFESSIONAL", "ft": ["QE"]}
        )
        short_query = "short query"
        request = {
            "search_input": short_query,
            "expand_search_input": True,
            "cache": False,
            "search_filters": {}
        }
        response = self.search_send_request('/search/semantic?show_general=1', request, 'post')
        assert response["data"]["search_info"]["details"]['semantic']['expanded_search_input']

    @classmethod
    def get_document_id_query(cls):
        return {
            "documents_ids": family_ids + [random.randint(1, 1000000000) * -1]
        }

    @classmethod
    def _generate_unique_id(cls):
        return re.sub(r'\W+', '', str(datetime.now()))

    def _perform_citation_flat_search(self):
        request = self.get_citation_query()
        response = self.search_send_request('/web/search/citation_flat?show_citations=1&include_focal=1',
                                            request, 'post', HTTPStatus.OK)
        return response

    def _perform_semantic_search(self):
        request = self.get_semantic_query()
        response = self.search_send_request('/web/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post', HTTPStatus.OK)
        return response

    def _perform_boolean_search(self):
        request = self.get_boolean_query()
        response = self.search_send_request('/web/search/boolean?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post', HTTPStatus.OK)
        return response

    def _perform_npl_search(self):
        request = self.get_npl_query()
        response = self.search_send_request('/web/search/npl_v2',
                                            request, 'post', HTTPStatus.OK)
        return response

    def test_document_id_search(self):
        request = self.get_document_id_query()
        response = self.search_send_request(f'{url.DOCUMENT_ID_SEARCH_URL}', request, 'post', HTTPStatus.OK)
        assert len(response["data"]["documents"])

    def test_single_document_id_search(self):
        self.search_send_request(f'{url.DOCUMENT_ID_SEARCH_URL}/00000000', '', 'get', HTTPStatus.NOT_FOUND)
        response = self.search_send_request(f'{url.DOCUMENT_ID_SEARCH_URL}/{family_id}', '', 'get', HTTPStatus.OK)
        assert response["data"]["document"]
        assert response["data"]["document"]['analytics']
        assert response["data"]["document"]['bibliographic']
        assert response["data"]["document"]['general']
        assert response["data"]["document"]['general']['docdb_family_id'] == family_id
        assert response["data"]["document"]['legal_statuses']

    def test_boolean_search_with_fields(self):
        response = self.search_send_request('/web/search/boolean?show_analytics=1&show_general=1&show_bibliographic=1',
                                            data.search_boolean_fields, 'post', HTTPStatus.OK)
        assert len(response["data"]["documents"])
        assert "publications" not in response["data"]

    def test_boolean_search_with_publication(self):
        request = self.get_boolean_query(has_filters=False)
        request['search_document_type'] = "PUBLICATION"
        response = self.search_send_request('/web/search/boolean?show_analytics=1&show_general=1&show_bibliographic=1',
                                            request, 'post', HTTPStatus.OK)
        assert len(response["data"]["publications"])
        assert "publications" in response["data"]
        assert "documents" not in response["data"]

    def test_citation_flat_search(self):
        response = self._perform_citation_flat_search()
        assert len(response["data"]["documents"])

    def test_patent_number_search(self):
        request = self.get_patent_number_query()
        response = self.search_send_request('/web/search/patent_number', request, 'post', HTTPStatus.OK)
        assert len(response['data']['documents'])

    def test_public_semantic_search(self):
        search_input = self.get_semantic_query()['search_input']
        email = '{0}@example.com'.format(self._generate_unique_id())
        payload = {
            'search_input': ' '.join([search_input] * 16),
            "first_name": "Hey",
            "last_name": "Abc",
            "company_name": "Octimine",
            "email": email,
            "country": "indiA",
            "state": "abc123"
        }
        self.search_send_request('/web/public_search/semantic', payload, 'post', HTTPStatus.BAD_REQUEST)

        payload['captcha_token'] = "abc123"
        self.search_send_request('/web/public_search/semantic', payload, 'post', HTTPStatus.NO_CONTENT)

    def test_read_document(self):
        self.search_send_request('/web/read_document', '', 'delete', HTTPStatus.NO_CONTENT)
        self.search_send_request('/web/read_document/', '', "get", HTTPStatus.NOT_FOUND)
        self.search_send_request('/web/read_document/popstar', '', "get", HTTPStatus.NOT_FOUND)
        response = self.search_send_request('/web/read_document', '', 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 0

        self.search_send_request(f"/web/read_document/{family_ids[0]}", "", "put", HTTPStatus.OK)
        self.search_send_request(f"/web/read_document/{family_ids[1]}", "", "put", HTTPStatus.OK)

        response = self.search_send_request("/web/read_document", '', "get", HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 2

        response = self.search_send_request("/web/read_document/team", '', "get", HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 2

        self.search_send_request(f'/web/read_document/{family_ids[0]}', '', 'delete', HTTPStatus.NO_CONTENT)

        response = self.search_send_request('/web/read_document', '', 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 1

        self.search_send_request(f"/web/read_document/{family_ids[0]}", "", "put", HTTPStatus.OK)
        self.search_send_request('/web/read_document', '', 'delete', HTTPStatus.NO_CONTENT)

        response = self.search_send_request('/web/read_document', '', 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 0

    def test_smart_highlights(self):
        response = self._perform_semantic_search()
        document_id = response['data']['documents'][0]['general']['docdb_family_id']
        payload = {"document_id": document_id, 'search_hash': self._get_search_hash(response),
                   'top_highest': 6}
        response = self.search_send_request('/web/patent/smart_highlights', payload, 'post', HTTPStatus.OK)

        res_data = response['data']

        doc_fields = ['description', 'claims']
        for field in doc_fields:
            assert field in res_data
            assert all(0 <= i <= 1 for i in list(map(lambda c: c['score'], res_data[field])))

    def test_extract_claims(self):
        payload = {"document_ids": [family_ids[0]], 'claims_quantity': 2}
        response = self.search_send_request('/web/patent/extract_claims', payload, 'post', HTTPStatus.OK)
        res_data = response['data']
        assert len(res_data['claims'][str(family_ids[0])]) == 2

    def test_async_export_results(self):
        semantic_response = self.search_send_request('/web/search/semantic', self.get_semantic_query(),
                                                     'post', HTTPStatus.OK)
        semantic_search_hash = semantic_response['data']['search_info']['search_hash']
        url = f'/web/export/{semantic_search_hash}?format=pdf'
        payload = {"patent_documents_ids": [36649762]}
        response = self.search_get_server_response(url, payload, 'post', allow_redirects=False)
        assert response.status_code == HTTPStatus.ACCEPTED
        url = response.headers["Location"]
        for i in range(5):
            response = self.search_get_server_response(url, None, 'get', allow_redirects=False)
            payload = response.json()
            if payload['status'] == 'FAILURE':
                assert False, 'Export task failed'
            if payload['status'] == 'SUCCESS':
                url = response.headers["Location"]
                response = self.search_get_server_response(url, None, 'get', allow_redirects=False)
                assert response.status_code == HTTPStatus.OK
                assert response.headers["Content-Type"] == 'application/pdf'
                break
            time.sleep(1)
        else:
            assert False, 'Max retries reached'

    def test_boolean_templates(self):
        existing_boolean_templates = len(self._list_boolean_templates()['data']['boolean_templates'])
        standard_query = "[{\"field\": \"xx\", \"operator\": \"=\", \"value\": \"xx\"}]"
        boolean_template = self._create_boolean_template(random_word(10), standard_query, 'STANDARD')
        assert len(self._list_boolean_templates()['data']['boolean_templates']) == existing_boolean_templates + 1
        title = random_word(10)
        advanced_query = "TITLE=abc AND ABSTRACT=xyz"
        self._update_boolean_template(boolean_template["id"], title, advanced_query, 'ADVANCED')
        boolean_template = self._get_boolean_template(boolean_template["id"], HTTPStatus.OK)
        assert boolean_template["title"] == title
        assert boolean_template["content"] == advanced_query
        assert boolean_template["type"] == "ADVANCED"
        self._delete_boolean_template(boolean_template["id"])
        self._get_boolean_template(boolean_template["id"], HTTPStatus.NOT_FOUND)

    def test_history(self):
        history_base_url = '/web/history'
        self.send_request(f'{history_base_url}', None, 'delete', HTTPStatus.NO_CONTENT)
        response = self.send_request(history_base_url, None, 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 0
        # Create history entries
        self.send_request('/web/search/semantic?save_history=1', self.get_semantic_query(), 'post', HTTPStatus.OK)
        self.send_request('/web/search/boolean?save_history=1', self.get_boolean_query(), 'post', HTTPStatus.OK)
        self.send_request('/web/search/citation_flat?save_history=1', self.get_citation_query(), 'post', HTTPStatus.OK)
        self.send_request('/web/search/npl_v2', self.get_npl_query(), 'post', HTTPStatus.OK)

        # Test filter and sorting
        self.send_request(
            f'{history_base_url}?sort_column=created_at&sort_order=desc&search_input=like:{quote("%Test%")}',
            None, 'get', HTTPStatus.OK
        )

        response = self.send_request(f'{history_base_url}', None, 'get', HTTPStatus.OK)
        total_hits = response['data']['page']['total_hits']
        assert total_hits

        # Get single entry
        response = self.send_request(f"{history_base_url}/{str(response['data']['searches'][0]['id'])}",
                                     None, 'get', HTTPStatus.OK)
        # Delete single entry
        self.send_request(f"{history_base_url}/{str(response['data']['id'])}", None, 'delete',
                          HTTPStatus.NO_CONTENT)

        response = self.send_request(f'{history_base_url}', None, 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == total_hits - 1

        # Delete all entries
        self.send_request(f'{history_base_url}', None, 'delete', HTTPStatus.NO_CONTENT)
        response = self.send_request(f'{history_base_url}', None, 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 0
