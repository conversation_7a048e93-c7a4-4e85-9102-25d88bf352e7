import uuid
from datetime import datetime
from http import HTT<PERSON>tatus
from typing import List
from urllib.parse import urlencode

from app.models.collaboration.collaboration import CollaborationResourceType, CollaborationPermission
from app.models.result_collection.collection import CollectionType
from tests.integration_tests.base_integration_test import TestIntegration, family_id, publication_numbers

from tests.unit_tests.search_api import data
from tests.unit_tests.web_api import url
from tests.unit_tests.web_api.test_subsample import random_word


class TestIntegrationCollections(TestIntegration):

    def _perform_boolean_search(self):
        request = self.get_boolean_query()
        response = self.send_request('/web/search/boolean?show_analytics=1&show_general=1&show_bibliographic=1',
                                     request, 'post', HTTPStatus.OK)
        return response

    @classmethod
    def get_citation_query(cls, level=1):
        cache_breaker = cls.break_cache()
        return {
            "patent_numbers": [
                "GB-0027280-D0",
                "EP-0926872-A2",
                "JP-H10188004-A",
                "WO2018006959A1",
                "DE-%s-A1" % cache_breaker
            ],
            "direction": "bidirectional",
            "level": level
        }

    def _create_result_collection(self, name=None, document_ids=None, monitor_run_id=None,
                                  search_history_id=None, search_hash=None,
                                  publication_numbers=None, collection_type=None):
        payload = {
            "name": name or "Test Collection",
            "search_history_id": search_history_id,
            "monitor_run_id": monitor_run_id,
            "search_hash": search_hash,
            "publication_numbers": publication_numbers,
            "collection_type": collection_type,
            "document_ids": document_ids
        }
        payload = {k: v for k, v in payload.items() if v}
        response = self.send_request('/web/result_collections', payload, 'post', HTTPStatus.CREATED)
        return response["data"]

    @classmethod
    def generate_share_code(cls):
        return str(uuid.uuid4())

    def _assign_collection_to_folder(self, collection_id, folder_id):
        payload = {"folder_id": folder_id}
        response = self.send_request(f"/web/result_collections/{collection_id}", payload, "patch", HTTPStatus.OK)
        return response["data"]

    def _get_folder(self, folder_id, expected_status=HTTPStatus.OK, access_token=None):
        res = self.send_request(
            f"/web/result_collections/folders/{folder_id}", "", "get", expected_status, access_token
        )
        return res["data"] if 'data' in res else res

    def _create_folder(self, name, description, parent_id=None):
        payload = {"name": name, "description": description, "parent_id": parent_id}
        res = self.send_request("/web/result_collections/folders", payload, "post", HTTPStatus.CREATED)
        return res["data"]

    def _update_folder(self, folder_id, name, description):
        payload = {"name": name, "description": description}
        response = self.send_request(f"/web/result_collections/folders/{folder_id}", payload, "patch", HTTPStatus.OK)
        return response["data"]

    def _delete_folder(self, folder_id):
        self.send_request(f"/web/result_collections/folders/{folder_id}", "", "delete", HTTPStatus.NO_CONTENT)

    def _list_shared_folders(self, name=None, folder_id=None, access_token=None):
        params = {k: v for k, v in {'name': name, 'folder_id': folder_id}.items() if v is not None}
        url = "/web/result_collections/folders/shared"
        if params:
            url = f"{url}?{urlencode(params)}"
        return self.send_request(url, "", "get", HTTPStatus.OK, access_token=access_token)

    def _share_folder(self, folder_id, user_ids=None, group_ids=None):
        payload = {}
        if user_ids:
            payload['user_ids'] = user_ids
        if group_ids:
            payload['group_ids'] = group_ids
        self.send_request(f"/web/result_collections/folders/{folder_id}/share", payload, "post", HTTPStatus.NO_CONTENT)

    def _perform_semantic_search(self):
        request = self.get_semantic_query()
        response = self.send_request('/web/search/semantic?show_analytics=1&show_general=1&show_bibliographic=1',
                                     request, 'post', HTTPStatus.OK)
        return response

    def _get_result_collection_by_share_code(self, share_code, expected_status=HTTPStatus.OK):
        response = self.send_request(f'/web/result_collections/shared/{share_code}', '', 'get', expected_status, None,
                                     True)
        return response

    def _test_users_of_collaboration_resource(self, user_id: int, resource_id: int,
                                              resource_type: CollaborationResourceType, expected_user_ids: List[int]):
        access_token = self._create_access_token(is_admin=False, user_id=user_id)
        _url = f'/web/collaborations/collaborators?resource_id={resource_id}&resource_type={resource_type.value}' \
               f'&collaborator_type=USER'
        resp = self.send_request(_url, None, "get", HTTPStatus.OK, access_token)
        assert set(expected_user_ids) == set([u['id'] for u in resp['data']['collaborators']])

    def _test_collaborations(self, resource_id: int, resource_type: CollaborationResourceType):
        user_ids = [2, 3]
        self._share_resource({'resource_id': resource_id, 'resource_type': resource_type.name,
                             "permission": CollaborationPermission.READONLY.name, "user_ids": user_ids})
        self._test_users_of_collaboration_resource(data.user_id, resource_id, resource_type, user_ids)
        self._share_resource({'resource_id': resource_id, 'resource_type': resource_type.name,
                             "permission": CollaborationPermission.READ_WRITE.name, "user_ids": user_ids})

    def _perform_citation_flat_search(self):
        request = self.get_citation_query()
        response = self.send_request('/web/search/citation_flat?show_citations=1&include_focal=1',
                                     request, 'post', HTTPStatus.OK)
        return response

    def _stop_sharing_folder(self, folder_id):
        self.send_request(f"/web/result_collections/folders/{folder_id}/share", None, "delete", HTTPStatus.NO_CONTENT)

    def _list_shared_collections(self, folder_id=None, access_token=None):
        url = f'/web/result_collections/shared?folder_id={folder_id or ""}'
        return self.send_request(url, '', 'get', HTTPStatus.OK, access_token=access_token)

    def _extract_patent_list(self, body):
        return self.send_request(url.EXTRACT_PATENT_LIST_URL, body, 'post', HTTPStatus.OK)

    def _get_result_collection(self, collection_id, expected_status=HTTPStatus.OK, access_token=None):
        collection_id = collection_id

        response = self.send_request(f'/web/result_collections/{collection_id}', '', 'get', expected_status,
                                     access_token)
        return response["data"] if expected_status == HTTPStatus.OK else response

    def _get_collaborations(self, user_id: int):
        access_token = self._create_access_token(is_admin=False, user_id=user_id)
        resp = self.send_request('/web/collaborations', '', "get", HTTPStatus.OK, access_token)
        return resp['data']['collaborations']

    def _update_result_collection(self, collection_id, name, description, share_code):
        payload = {
            "name": name,
            "description": description,
            "share_code": share_code
        }
        response = self.send_request(f'/web/result_collections/{collection_id}', payload, 'patch', HTTPStatus.OK)
        return response["data"]

    def _clone_result_collection(self, collection_id, name, description):
        payload = {
            "name": name,
            "description": description
        }
        response = self.send_request(f'/web/result_collections/{collection_id}/clone', payload, 'post', HTTPStatus.OK)
        return response["data"]

    def _load_result_collections(self, **kwargs):
        kwargs = {k: "is:null" if v is None else v for k, v in kwargs.items()}
        params = urlencode(kwargs)
        return self.send_request(f'/web/result_collections{"?" + params if params else ""}', '', 'get', HTTPStatus.OK)

    def _load_search_result_collections(self, **kwargs):
        kwargs = {k: "is:null" if v is None else v for k, v in kwargs.items()}
        params = urlencode(kwargs)
        return self.send_request(f'/web/result_collections/search{"?" + params if params else ""}', '', 'get',
                                 HTTPStatus.OK)

    def _share_collection(self, collection_id, user_ids=None, group_ids=None, permission=None,
                          expected_status=HTTPStatus.NO_CONTENT, access_token=None):
        payload = {
            'resource_id': collection_id,
            'resource_type': 'COLLECTION',
        }
        if user_ids:
            payload['user_ids'] = user_ids
        if group_ids:
            payload['group_ids'] = group_ids
        if permission:
            payload['permission'] = permission
        self._share_resource(payload, expected_status, access_token=access_token)

    def _delete_result_collection(self, collection_id, expected_status=HTTPStatus.NO_CONTENT, access_token=None):
        self.send_request(f'/web/result_collections/{collection_id}', "", "delete", expected_status, access_token)

    def _list_result_collection_documents(self, collection_id, user_id, expected_http_status=HTTPStatus.OK):
        access_token = self._create_access_token(is_admin=False, user_id=user_id)
        return self.send_request(f'/web/result_collections/{collection_id}/documents', "", "get", expected_http_status,
                                 access_token)

    def _add_documents_to_collection(self, collection_id, user_id, document_ids=None, search_hash=None,
                                     expected_http_status=HTTPStatus.NO_CONTENT,
                                     publication_numbers=None):
        payload = {"add": {"document_ids": document_ids, "search_hash": search_hash}}
        if publication_numbers:
            payload["add"].pop("document_ids", None)
            payload["add"]["publication_numbers"] = publication_numbers
        access_token = self._create_access_token(is_admin=False, user_id=user_id)
        self.send_request(f'/web/result_collections/{collection_id}/documents', payload, "patch", expected_http_status,
                          access_token)

    def _update_documents_from_collection(self, collection_id, user_id, expected_http_status=HTTPStatus.NO_CONTENT):
        payload = {"update": {"id": 1, collection_id: 1, "similarity_index": 2, "confidence_score": .7}}
        access_token = self._create_access_token(is_admin=False, user_id=user_id)
        self.send_request(f'/web/result_collections/{collection_id}/documents', payload, "patch", expected_http_status,
                          access_token)

    def _remove_documents_from_collection(self, collection_id, document_ids, user_id,
                                          expected_http_status=HTTPStatus.NO_CONTENT,
                                          publication_numbers=None):
        payload = {"remove": {"document_ids": document_ids}}
        if publication_numbers:
            payload["remove"].pop("document_ids", None)
            payload["remove"]["publication_numbers"] = publication_numbers
        access_token = self._create_access_token(is_admin=False, user_id=user_id)
        self.send_request(f'/web/result_collections/{collection_id}/documents', payload, "patch", expected_http_status,
                          access_token)

    def _aggregate_results_collections(self, operation, *collections):
        payload = {
            "name": "Aggregated collection",
            "collection_ids": [c["id"] for c in collections],
            "operation": operation
        }
        response = self.send_request('/web/result_collections/aggregate', payload, 'post', HTTPStatus.CREATED)
        return response["data"]

    def _convert_results_collection(self, operation, collection_id, user_id, profile_name=None,
                                    profile_category=None, expected_status=HTTPStatus.CREATED):
        payload = {
            "operation": operation,
            "profile_name": profile_name,
            "profile_category": profile_category
        }
        access_token = self._create_access_token(is_admin=False, user_id=user_id)
        response = self.send_request(f'/web/result_collections/convert/{collection_id}', payload, 'post',
                                     expected_status, access_token)
        return response["data"]

    def test_result_collections(self):
        semantic_search = self._perform_semantic_search()
        semantic_hash = self._get_search_hash(semantic_search)
        boolean_search = self._perform_boolean_search()
        boolean_hash = self._get_search_hash(boolean_search)
        citation_search = self._perform_citation_flat_search()
        citation_hash = self._get_search_hash(citation_search)
        existing_collections = self._get_total_hits(self._load_result_collections())
        uid = self._generate_unique_id()
        collection_one = self._create_result_collection(name=f"doc ids {uid}", document_ids=[family_id])
        collection_two = self._create_result_collection(name=f"semantic hash {uid}", search_hash=semantic_hash)
        collection_three = self._create_result_collection(name=f"boolean hash {uid}", search_hash=boolean_hash)
        collection_four = self._create_result_collection(name=f"citation hash {uid}", search_hash=citation_hash)
        new_collections = self._get_total_hits(self._load_result_collections())
        assert collection_one["results_count"] == 1
        assert collection_two["results_count"] == self._get_total_hits(semantic_search)
        assert collection_three["results_count"] == self._get_total_hits(boolean_search)
        assert collection_four["results_count"] == self._get_total_hits(citation_search)
        assert new_collections == existing_collections + 4
        share_code = self.generate_share_code()
        updated_name = f"updated name {self._generate_unique_id()}"
        self._update_result_collection(collection_one["id"], updated_name, "updated_description", share_code)
        collection_one = self._get_result_collection(collection_one["id"])
        assert collection_one["name"] == updated_name
        assert collection_one["description"] == "updated_description"
        assert collection_one["share_code"] == share_code
        cloned_collection = self._clone_result_collection(collection_one["id"], f"cloned {uid}", "cloned description")
        assert cloned_collection["collection_type"] == collection_one["collection_type"]
        collection_one = self._get_result_collection(cloned_collection["id"])
        assert collection_one["name"] == f"cloned {uid}"
        assert collection_one["description"] == "cloned description"
        data_by_share_code = self._get_result_collection_by_share_code(share_code)
        assert 'data' in data_by_share_code
        assert 'auth' in data_by_share_code
        self.send_request(f'/web/result_collections/{data_by_share_code["data"]["id"]}/documents',
                          None, 'get', HTTPStatus.OK, data_by_share_code['auth']['access_token'])
        self._delete_result_collection(data_by_share_code['data']["id"])
        new_collections = self._get_total_hits(self._load_result_collections())
        assert new_collections == existing_collections + 4

    def test_collection_permissions(self):
        uid = self._generate_unique_id()
        collection = self._create_result_collection(name=f"collection {uid}", document_ids=[family_id])
        assert collection['permissions'] == ['read', 'write', 'share']

        team_member = self._create_access_token(user_id=2, company_id=1)

        self._share_collection(collection["id"], user_ids=[2], permission='READONLY')
        shared_collection = self._get_result_collection(collection['id'], access_token=team_member)
        assert shared_collection['permissions'] == ['read', 'share']

        self._share_collection(
            collection["id"], user_ids=[3],
            permission='READ_WRITE', access_token=team_member,
            expected_status=HTTPStatus.FORBIDDEN
        )

        self._share_collection(collection["id"], user_ids=[3], permission='READONLY', access_token=team_member)

        self._share_collection(collection["id"], user_ids=[2], permission='READ_WRITE')
        shared_collection = self._get_result_collection(collection['id'], access_token=team_member)
        assert shared_collection['permissions'] == ['read', 'write', 'share']

        self._delete_result_collection(collection["id"], access_token=team_member)

    def test_result_collections_by_publication_number(self):
        existing_collections = self._get_total_hits(self._load_result_collections())
        collection = self._create_result_collection(name=f'{datetime.now().timestamp()}-collection',
                                                    collection_type=CollectionType.PUBLICATION,
                                                    publication_numbers=["AU-2019271927-A1"])
        new_collections = self._get_total_hits(self._load_result_collections())
        assert collection["results_count"] == 1
        assert new_collections == existing_collections + 1
        self._delete_result_collection(collection["id"])

        new_collections = self._get_total_hits(self._load_result_collections())
        assert new_collections == existing_collections

    def test_result_collections_aggregation(self):
        uid = self._generate_unique_id()
        collection_one = self._create_result_collection(name=f"Agg - one {uid}", document_ids=[family_id])
        collection_two = self._create_result_collection(name=f"Agg - two {uid}", document_ids=[family_id])
        collection_three = self._create_result_collection(name=f"Agg - three {uid}", document_ids=[family_id])
        union = self._aggregate_results_collections("UNION", collection_one, collection_two, collection_three)
        intersection = self._aggregate_results_collections("INTERSECTION", collection_one, collection_two,
                                                           collection_three)
        diff = self._aggregate_results_collections("DIFF", collection_one, collection_two, collection_three)
        assert union["results_count"] == 1
        assert intersection["results_count"] == 1
        assert diff["results_count"] == 0

    def test_result_collections_convert(self):
        uid = self._generate_unique_id()
        collection = self._create_result_collection(name=f"Convert {uid}", document_ids=[family_id])
        landscape = self._convert_results_collection("LANDSCAPE", collection['id'],
                                                     data.user_id, f"Convert to landscape {uid}", "test")
        monitor = self._convert_results_collection("MONITOR", collection['id'], data.user_id)
        assert landscape["status"] == "DRAFT"
        assert landscape["name"] == f"Convert to landscape {uid}"
        assert landscape["category"] == "test"
        assert monitor["machine_learning_status"] == "Pending"

    def test_result_collections_documents(self):
        collection = self._create_result_collection(name=f'{datetime.now().timestamp()}-collection')
        self._add_documents_to_collection(collection["id"], data.user_id, document_ids=[family_id])
        self._list_result_collection_documents(collection["id"], data.user_id)
        self._remove_documents_from_collection(collection["id"], [family_id], data.user_id)
        semantic_search = self._perform_semantic_search()
        semantic_hash = self._get_search_hash(semantic_search)
        self._add_documents_to_collection(collection["id"], data.user_id, search_hash=semantic_hash)
        self._delete_result_collection(collection["id"])

    def test_result_collections_documents_by_publication_numbers(self):
        collection = self._create_result_collection(name=f'{datetime.now().timestamp()}-collection',
                                                    collection_type=CollectionType.PUBLICATION)
        self._add_documents_to_collection(collection["id"], user_id=data.user_id,
                                          publication_numbers=[publication_numbers[0]])
        self._list_result_collection_documents(collection["id"], data.user_id)
        self._remove_documents_from_collection(collection["id"], None, user_id=data.user_id,
                                               publication_numbers=[publication_numbers[0]])
        self._delete_result_collection(collection["id"])

    def test_collection_folders(self):
        existing_folders = len(self._list_folders()['data']['folders'])
        uid = self._generate_unique_id()
        collection = self._create_result_collection(f"Test collection folders {uid}")
        folder = self._create_folder(random_word(10), "test description")
        assert len(self._list_folders()['data']['folders']) == existing_folders + 1
        name = random_word(10)
        self._update_folder(folder["id"], name, "updated desc")
        folder = self._get_folder(folder["id"])
        assert folder["name"] == name
        assert folder["description"] == "updated desc"
        self._assign_collection_to_folder(collection["id"], folder["id"])
        assert self._get_total_hits(self._load_result_collections(folder_id=folder["id"], name=f'like:%{uid}%')) == 1
        assert self._get_total_hits(self._load_result_collections(name=f'like:%{uid}%')) == 0
        assert self._get_total_hits(self._load_search_result_collections(exclude_hierarchy=1,
                                                                         name=f'like:%{uid}%')) == 1
        folder = self._get_folder(folder["id"])
        assert folder["collections_count"] == 1
        collaborations_count = len(self._get_collaborations(user_id=1))
        self._test_collaborations(collection["id"], CollaborationResourceType.COLLECTION)
        self._delete_folder(folder["id"])
        assert len(self._get_collaborations(user_id=1)) == collaborations_count
        self._get_result_collection(collection["id"], expected_status=HTTPStatus.NOT_FOUND)

    def test_shared_collections_inside_nested_folders(self):
        uid = self._generate_unique_id()
        parent_folder = self._create_folder(f"folder - {uid}", "test description")
        child_folder = self._create_folder(f"child folder - {uid}", "test description",
                                           parent_id=parent_folder['id'])
        grandchild_folder = self._create_folder(
            f"grandchild folder - {uid}", "test description", parent_id=child_folder['id']
        )
        collection = self._create_result_collection(f"collection {uid}")
        collection_2 = self._create_result_collection(f"collection 2 {uid}")
        self._assign_collection_to_folder(collection["id"], grandchild_folder["id"])
        self._assign_collection_to_folder(collection_2["id"], grandchild_folder["id"])

        # I am only sharing one collection out of the two inside the folder
        self._share_collection(collection["id"], user_ids=[2])

        team_member = self._create_access_token(user_id=2, company_id=1)

        # should be able to get info about intermediate folders up to the shared collections
        self._get_folder(parent_folder["id"], access_token=team_member)
        self._get_folder(child_folder["id"], access_token=team_member)
        self._get_folder(grandchild_folder["id"], access_token=team_member)

        # should be able to see all the way to the shared collection
        shared_folders = self._list_shared_folders(
            parent_folder['name'], folder_id=None, access_token=team_member
        )['data']['folders']
        assert [f for f in shared_folders if f['id'] == parent_folder['id']]

        shared_folders = self._list_shared_folders(
            child_folder['name'], folder_id=parent_folder['id'], access_token=team_member
        )['data']['folders']
        assert [f for f in shared_folders if f['id'] == child_folder['id']]

        shared_folders = self._list_shared_folders(
            grandchild_folder['name'], folder_id=child_folder['id'], access_token=team_member
        )['data']['folders']
        assert [f for f in shared_folders if f['id'] == grandchild_folder['id']]

        # also should be able to access collections inside that folder
        shared_collections = self._list_shared_collections(
            grandchild_folder['id'], team_member
        )['data']['result_collections']
        assert [c for c in shared_collections if c['id'] == collection['id']]
        # We should not be able to see collection 2, since it was not shared
        assert not [c for c in shared_collections if c['id'] == collection_2['id']]

    def test_extract_collection_patent_list(self):
        boolean_search = self._perform_boolean_search()
        boolean_hash = self._get_search_hash(boolean_search)
        collection = self._create_result_collection(name="Extract patents", search_hash=boolean_hash)
        response = self._extract_patent_list({'collection_id': collection["id"]})
        self._delete_result_collection(collection["id"])
        assert len(response['data']['patent_list']) == self._get_total_hits(boolean_search)

    def test_folder_sharing(self):
        uid = self._generate_unique_id()
        collection = self._create_result_collection(f"collection {uid}")
        folder = self._create_folder(f"folder {uid}", "test description")
        folder_2 = self._create_folder(f"folder 2 {uid}", "test description")
        self._assign_collection_to_folder(collection["id"], folder["id"])
        self._share_folder(folder['id'], user_ids=[2])

        team_member = self._create_access_token(user_id=2, company_id=1)
        # should be able to see the folder
        shared_folders = self._list_shared_folders(access_token=team_member)['data']['folders']
        assert [f for f in shared_folders if f['id'] == folder['id']]
        assert not [f for f in shared_folders if f['id'] == folder_2['id']]  # not shared, should not be seen

        # also should be able to access collections inside that folder
        shared_collections = self._list_shared_collections(
            folder['id'], access_token=team_member
        )['data']['result_collections']
        assert [c for c in shared_collections if c['id'] == collection['id']]

        # if folder is not shared anymore, access should be lost for it
        self._stop_sharing_folder(folder['id'])
        shared_folders = self._list_shared_folders(folder['name'], access_token=team_member)['data']['folders']
        assert not shared_folders

        # access should also be lost for collections inside it and not explicitly shared
        shared_collections = self._list_shared_collections(
            folder['id'], access_token=team_member
        )['data']['result_collections']
        assert not shared_collections

    def test_nested_folders(self):
        uid = self._generate_unique_id()
        parent_folder = self._create_folder(f"folder - {uid}", "test description")
        child_folder_1 = self._create_folder(
            f"child folder 1 - {uid}", "test description", parent_id=parent_folder['id']
        )
        child_folder_2 = self._create_folder(
            f"child folder 2 - {uid}", "test description", parent_id=parent_folder['id']
        )
        grandchild_folder = self._create_folder(
            f"grandchild folder - {uid}", "test description", parent_id=child_folder_1['id']
        )

        self._get_folder(parent_folder["id"])
        self._get_folder(child_folder_1["id"])
        self._get_folder(child_folder_2["id"])
        self._get_folder(grandchild_folder["id"])

        folders = self._list_folders()['data']['folders']
        assert [f for f in folders if f['name'] == parent_folder['name']]
        folders = self._list_folders(folder_id=parent_folder['id'])['data']['folders']
        assert [f for f in folders if f['name'] == child_folder_1['name']]
        assert [f for f in folders if f['name'] == child_folder_2['name']]
        folders = self._list_folders(folder_id=child_folder_1['id'])['data']['folders']
        assert [f for f in folders if f['name'] == grandchild_folder['name']]

        collection = self._create_result_collection(f"Test collection folders {uid}")
        self._assign_collection_to_folder(collection["id"], grandchild_folder["id"])

        self._delete_folder(parent_folder["id"])

        # Folders and collections in the hierarchy should be also gone
        self._get_folder(child_folder_1["id"], expected_status=HTTPStatus.NOT_FOUND)
        self._get_folder(child_folder_2["id"], expected_status=HTTPStatus.NOT_FOUND)
        self._get_folder(grandchild_folder["id"], expected_status=HTTPStatus.NOT_FOUND)
        self._get_result_collection(collection["id"], expected_status=HTTPStatus.NOT_FOUND)

    def test_nested_folders_sharing(self):
        uid = self._generate_unique_id()
        parent_folder = self._create_folder(f"folder - {uid}", "test description")
        child_folder = self._create_folder(f"child folder - {uid}", "test description", parent_id=parent_folder['id'])
        grandchild_folder = self._create_folder(
            f"grandchild folder - {uid}", "test description", parent_id=child_folder['id']
        )
        collection = self._create_result_collection(f"collection {uid}")
        self._assign_collection_to_folder(collection["id"], grandchild_folder["id"])
        self._share_folder(grandchild_folder['id'], user_ids=[2])

        team_member = self._create_access_token(user_id=2, company_id=1)
        # should be able to see all the way to the folder
        shared_folders = self._list_shared_folders(
            parent_folder['name'], folder_id=None, access_token=team_member
        )['data']['folders']
        assert [f for f in shared_folders if f['id'] == parent_folder['id']]

        shared_folders = self._list_shared_folders(
            child_folder['name'], folder_id=parent_folder['id'], access_token=team_member
        )['data']['folders']
        assert [f for f in shared_folders if f['id'] == child_folder['id']]

        shared_folders = self._list_shared_folders(
            grandchild_folder['name'], folder_id=child_folder['id'], access_token=team_member
        )['data']['folders']
        assert [f for f in shared_folders if f['id'] == grandchild_folder['id']]

        # also should be able to access collections inside that folder
        shared_collections = self._list_shared_collections(
            grandchild_folder['id'], team_member
        )['data']['result_collections']
        assert [c for c in shared_collections if c['id'] == collection['id']]

        self._delete_folder(parent_folder["id"])
