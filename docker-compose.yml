services:
  redis:
    image: redis:7.0.12-alpine
    command: redis-server --port 6389 --requirepass dhdJD232sHSJAHDKIS
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    ports:
      - 6389:6389
    networks:
      - internal

  mysql:
    image: mariadb:10.5.3-focal
    command: mysqld --sql_mode="NO_ENGINE_SUBSTITUTION" --character-set-server=utf8mb4 --collation-server=utf8mb4_general_ci
    environment:
      MYSQL_ROOT_PASSWORD: 62aHSjdKaJs728SjalsJ
      MYSQL_DATABASE: octimine_auth
      MYSQL_USER: cherry
      MYSQL_PASSWORD: ASDFNJxn4583fdsDHSKns736
    volumes:
      - ./init_db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - 3306:3306
    networks:
      - internal

  rabbitmq:
    image: rabbitmq:4.1.1-management-alpine
    environment:
      RABBITMQ_DEFAULT_USER: engine-dev
      RABBITMQ_DEFAULT_PASS: 2EGtEjecEzqaE857
    ports:
      - 5672:5672
      - 15672:15672
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - internal

  elasticsearch:
    image: registry.gitlab.com/octimine/shared_registry/elasticsearch-testdata:8.12.0
    ports:
      - 9200:9200
      - 9300:9300
    environment:
      discovery.type: single-node
    networks:
      - internal

  neo4j:
    image: registry.gitlab.com/octimine/shared_registry/neo4j-testdata
    environment:
      - NEO4J_AUTH=neo4j/test
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      - NEO4JLABS_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.\\\*
    ports:
      - 7474:7474
      - 7473:7473
      - 7687:7687
    networks:
      - internal

  mock-mysql:
    image: registry.gitlab.com/octimine/shared_registry/mock-mysql:latest
    command: mysqld --max_allowed_packet=64M --log_error=/var/log/mysql/general-log.log --character-set-server=utf8mb4 --collation-server=utf8mb4_general_ci --default-time-zone='+02:00'
    environment:
      MYSQL_ROOT_PASSWORD: NO^m3xUj
      MYSQL_DATABASE: laboratory
      MYSQL_USER: engine-dev
      MYSQL_PASSWORD: NO^m3xUj
    ports:
      - 4306:3306
    networks:
      - internal

  mock-search-engine:
    image: registry.gitlab.com/octimine/shared_registry/mock-search-engine:latest
    depends_on:
      - mock-mysql
      - rabbitmq
    networks:
      - internal

  swagger_ui:
    image: nginx
    ports:
      - 5003:80
    volumes:
      - ./docs:/usr/share/nginx/html

networks:
  internal:
    name: octimine-api-net
    ipam:
      driver: default
      config:
        - subnet: ***********/24
