import deepl
from dataclasses import dataclass
from flask import current_app
from deepl import DeepLException
from octimine_common.exceptions import BadRequestException


@dataclass
class TranslateRequest:
    source_language: str | None
    input: str


def translate(req: TranslateRequest) -> str | None:
    """
    Perform in-place translation of input text into english using DeepL API call
    """
    if req.source_language and req.source_language.lower() in ['en', 'en-gb', 'en-us']:
        return

    try:
        translator = deepl.Translator(current_app.config['DEEPL_API_KEY'])
        translation = translator.translate_text(text=req.input,
                                                source_lang=req.source_language,
                                                target_lang="EN-GB")
        return translation.text
    except DeepLException as dle:
        raise BadRequestException(message='DeepL error: ' + str(dle))
