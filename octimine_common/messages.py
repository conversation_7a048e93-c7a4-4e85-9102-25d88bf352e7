# Api error messages
ERROR_INVALID_CREDENTIALS = "Invalid username and password combination"
ERROR_BAD_REQUEST = "Bad Request"
ERROR_INTERNAL_SERVER_ERROR = "An internal server error has occurred"
ERROR_SERVICE_UNAVAILABLE = "At this time, the service is unavailable, please wait a few seconds and try again"
ERROR_RESOURCE_NOT_FOUND = "We couldn't find the specified resource"
ERROR_METHOD_NOT_ALLOWED = "The method is not allowed for the requested URL"
ERROR_FORBIDDEN = "The user is not authorized to perform this request"
ERROR_NOT_IMPLEMENTED = "Not implemented"
ERROR_UNAUTHORIZED = "Unauthorized"
ERROR_CONFLICT = "The request cannot be processed due to a conflict with the target resource"

# JWT
ERROR_REVOKED_TOKEN = "The user token has been revoked"
ERROR_INVALID_TOKEN = "The token is invalid"
ERROR_EXPIRED_TOKEN = "The token has expired"

# Sort
ERROR_SORT_NO_COLUMN = "Sort requested but no sort column given"
ERROR_SORT_ORDER = "Invalid sort order '%s', should be one of 'asc', 'desc"
ERROR_SORT_WRONG_COLUMN = "Invalid sort column '%s'"
ERROR_SORT_ES = "Asked to sort by '%s', but this field is not in the result set"
