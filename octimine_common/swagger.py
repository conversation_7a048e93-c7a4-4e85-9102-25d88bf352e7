import os
import yaml
from typing import Dict


class SwaggerDocParser:
    def __init__(self, base_path: str):
        self.updated_files = set()
        self.base_path = base_path

    def _load_yaml_file(self, path: str) -> Dict:
        with open(os.path.join(self.base_path, path)) as f:
            return yaml.safe_load(f)

    def _merge_dicts(self, dict1: Dict, dict2: Dict):
        for item, value in dict1.items():
            if item in dict2:
                if isinstance(dict2[item], dict):
                    self._merge_dicts(value, dict2.pop(item))
        for item, value in dict2.items():
            dict1[item] = value

    def _resolve_references(self, root_dict: Dict, doc_dict: Dict, prefix: str) -> None:
        for k, v in doc_dict.items():
            if isinstance(v, dict):
                self._resolve_references(root_dict, v, prefix)
            elif isinstance(v, list):
                for i in v:
                    if isinstance(i, dict):
                        self._resolve_references(root_dict, i, prefix)
            elif isinstance(v, str):
                if k == '$ref' and '#' in v and not v.startswith('#'):
                    parts = v.split('#')
                    doc_dict[k] = '#' + parts[1]
                    referenced_file_path = os.path.normpath(os.path.join(prefix, parts[0]))
                    if referenced_file_path not in self.updated_files:
                        referenced_file = self._load_yaml_file(referenced_file_path)
                        self.updated_files.add(referenced_file_path)
                        self._resolve_references(root_dict, referenced_file, os.path.dirname(referenced_file_path))
                        self._merge_dicts(root_dict, referenced_file)

    def parse_file(self, file_path: str) -> Dict:
        doc_root = self._load_yaml_file(file_path)
        yaml_files = ['../docs/definitions.yaml', '../docs/search_definitions.yaml', '../docs/filter_definitions.yaml']
        for def_yaml in yaml_files:
            self._merge_dicts(doc_root, self._load_yaml_file(os.path.join(os.path.dirname(os.path.realpath(__file__)),
                                                                          def_yaml)))
        for p in doc_root['paths']:
            for i in doc_root['paths'][p]:
                if i == '$ref':
                    ref_path = doc_root['paths'][p][i]
                    doc_root['paths'][p] = self._load_yaml_file(ref_path)
                    self._resolve_references(doc_root, doc_root['paths'][p], os.path.dirname(ref_path))
        return doc_root
