from collections import OrderedDict
from typing import List, Dict, Union
from http import HTTPStatus
from flask import make_response, Response
from octimine_common.util import Page


def create_response(data: Union[List, Dict], status: int = HTTPStatus.OK, **kwargs) -> Response:
    result = {
        "data": data,
        "status": status
    }
    if kwargs:
        result.update(kwargs)
    return make_response(result, result['status'])


def wrap_many(key: str = "results", data: Union[List, Dict] = None, page: Page = None) -> Dict:
    wrapper = OrderedDict({key: data})
    if page:
        wrapper['page'] = page.to_dict()
    return wrapper
