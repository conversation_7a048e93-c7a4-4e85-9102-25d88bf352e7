from flask import Blueprint
from flask.views import MethodView
import re


class Api:
    """
    This class mimics the already-retired Flask-Restful APIs, so that existing code does not break so badly.
    That gives us opportunity to retire the extension while having to change all client code at once.
    For registering new endpoints, please use flask blueprints directly instead of this class.
    """

    def __init__(self, blueprint: Blueprint, **_):
        self._blueprint = blueprint

    def add_resource(self, method_view_cls: type[MethodView], *url_rules: str, endpoint: str | None = None):
        for rule in url_rules:
            self._blueprint.add_url_rule(
                rule, endpoint, view_func=method_view_cls.as_view(self._url_rule_to_view_name(rule))
            )

    @staticmethod
    def _url_rule_to_view_name(url_rule: str) -> str:
        return re.sub(r'\W+', '', url_rule)
