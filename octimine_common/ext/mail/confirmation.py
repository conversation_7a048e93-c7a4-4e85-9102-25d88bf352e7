import logging
from datetime import datetime
from http import HTTPStatus

from flask import current_app
from flask_redis import FlaskRedis
from itsdangerous import URLSafeSerializer, Serializer, BadSignature

from app import messages
from app.messages import ERROR_INVALID_CONFIRMATION_TOKEN, ERROR_ACTIVATED_USER
from octimine_common.exceptions import APIException


class ConfirmationLink:
    @staticmethod
    def create(token_data: dict, age: int = None, store: FlaskRedis = None):
        with current_app.app_context(), current_app.test_request_context():
            if store:
                return StoredConfirmationToken(store=store).generate(token_data, age)
            return ConfirmationToken().generate(token_data, age)


class ConfirmationToken:
    TOKEN_SALT = None

    def __init__(self, serializer: Serializer = None):
        self.serializer = serializer or self.get_default_serializer()
        self.TOKEN_SALT = current_app.config['MAIL_TOKEN_SALT']

    @staticmethod
    def get_default_serializer():
        return URLSafeSerializer(current_app.config['MAIL_PASSWORD'])

    def generate(self, token_data: dict, age: int = None):
        age = age or current_app.config['MAX_CONFIRM_TOKEN_AGE']
        return self.serializer.dumps({
            **token_data,
            'created': int(datetime.now().timestamp() * 1000 * 1000),
            'age': age
        }, self.TOKEN_SALT)

    def validate(self, token: str) -> dict:
        try:
            token_data = self.serializer.loads(token, self.TOKEN_SALT)
            token_created = datetime.fromtimestamp(token_data["created"] / 1000.0 / 1000.0)
            token_age_in_minutes = (datetime.now() - token_created).total_seconds() / 60
            age = token_data.get('age') or current_app.config['MAX_CONFIRM_TOKEN_AGE']
            if token_age_in_minutes > age:
                raise InvalidConfirmationTokenException(message=messages.ERROR_EXPIRED_CONFIRMATION_TOKEN)
            return token_data
        except BadSignature:
            raise InvalidConfirmationTokenException()


class StoredConfirmationToken:

    def __init__(self, store: FlaskRedis):
        self._store = store
        self._confirmation_token = ConfirmationToken()

    def generate(self, token_data: dict, age: int = None):
        age = age or current_app.config['MAX_CONFIRM_TOKEN_AGE']
        token = self._confirmation_token.generate(token_data=token_data, age=age)
        self._store.set(token, 1, ex=age * 60)
        return token

    def validate(self, token: str) -> dict:
        if not self._store.exists(token):
            raise InvalidConfirmationTokenException(message=messages.ERROR_INVALID_CONFIRMATION_TOKEN)
        self._store.delete(token)
        return self._confirmation_token.validate(token=token)


class InvalidConfirmationTokenException(APIException):
    def __init__(self, message=ERROR_INVALID_CONFIRMATION_TOKEN, code=HTTPStatus.BAD_REQUEST):
        APIException.__init__(self, message=message, code=code)

    @staticmethod
    def get_log_level():
        return logging.WARNING


class ActivatedUserException(APIException):
    def __init__(self, message=ERROR_ACTIVATED_USER, code=HTTPStatus.BAD_REQUEST):
        APIException.__init__(self, message=message, code=code)

    @staticmethod
    def get_log_level():
        return logging.WARNING
