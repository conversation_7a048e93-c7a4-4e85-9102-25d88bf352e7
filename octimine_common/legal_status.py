from datetime import datetime
from collections import defaultdict

_SUPPORTED_AUTHORITIES = {'AU', 'CA', 'CN', 'DE', 'EP', 'ES', 'FR', 'GB', 'JP', 'US'}
_MAX_AGE_FOR_LEGAL_STATUS = 25 * 365  # 25 years as days
_GENERAL_STATUSES = {
    'active': 'valid',
    'active_reinstated': 'valid',
    'granted': 'valid',
    'in_force': 'valid',
    'pending': 'pending',
    'expired': 'invalid',
    'expired_fee_related': 'invalid',
    'expired_lifetime': 'invalid',
    'withdrawn': 'invalid',
    'withdrawn_after_issue': 'invalid',
    'abandoned': 'invalid',
    'ceased': 'invalid',
    'revoked': 'invalid',
    'not_in_force': 'invalid'
}

_DETAILED_STATUES: dict[str, list] = defaultdict(lambda: [])
for k, v in _GENERAL_STATUSES.items():
    _DETAILED_STATUES[v].append(k)


def format_legal_statuses(legal_statuses: list[str] | None):
    return make_statement(map_legal_statuses(legal_statuses))


def map_legal_statuses(legal_statuses: list[str] | None):
    if not legal_statuses:
        return ['unknown']
    return [map_legal_status(ls) for ls in legal_statuses]


def map_legal_status(legal_status: str) -> str:
    """
    Map legal status of document in ES into one of predefined values (valid/invalid/pending/unknown)
    """
    return _GENERAL_STATUSES.get(legal_status, 'unknown')


def get_detailed_statuses(legal_status: str) -> list[str]:
    return _DETAILED_STATUES.get(legal_status, ['unknown'])


def make_statement(legal_statuses: list[str] | None) -> list[str]:
    legal_statuses = legal_statuses or []
    if 'valid' in legal_statuses or 'pending' in legal_statuses:
        return ['valid']
    elif 'invalid' in legal_statuses and 'unknown' in legal_statuses:
        return ['invalid', 'unknown']
    elif 'invalid' in legal_statuses:
        return ['invalid']
    else:
        return ['unknown']


def for_charts(legal_status_statement: list[str]):
    if 'invalid' in legal_status_statement and 'unknown' in legal_status_statement:
        return ['invalid']
    return legal_status_statement


def get_supported_authorities():
    return _SUPPORTED_AUTHORITIES


def is_applicable(*, publication_number: str, publication_date: datetime | None):
    if publication_number[:2].upper() not in _SUPPORTED_AUTHORITIES:
        return False
    if not publication_date:
        return True  # Better to say it's subject if we don't have the publication_date for some reason
    return (datetime.now() - publication_date).days <= _MAX_AGE_FOR_LEGAL_STATUS
