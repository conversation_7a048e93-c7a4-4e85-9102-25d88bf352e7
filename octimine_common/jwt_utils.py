from datetime import timed<PERSON><PERSON>
from flask_jwt_extended import get_jwt
from flask_jwt_extended import create_access_token as _create_access_token, get_jti


def get_jwt_claims():
    return get_jwt().get('user_claims', {})


def create_access_token(*, identity: str, claims: dict, expires_in: timedelta, session_store=None) -> str:
    access_token = _create_access_token(identity=identity, expires_delta=expires_in,
                                        additional_claims={'user_claims': claims})
    if session_store:
        jti = get_jti(encoded_token=access_token)
        session_store.set(jti, 'false', int(expires_in.total_seconds()))
    return access_token
