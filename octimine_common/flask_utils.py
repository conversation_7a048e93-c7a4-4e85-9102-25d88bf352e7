import base64
import json
import logging.config

from flask import g


class ProxyInfoExtractorApp(object):
    """
    In case the app is running behind the reverse proxy, extract original protocol, server and path information from
    headers set by reverse proxy so the app knows its real base URL and how to format the redirects
    """
    def __init__(self, app):
        self.app = app

    def __call__(self, environ, start_response):
        script_name = environ.get('HTTP_X_FORWARDED_PREFIX', '')
        if script_name:
            environ['SCRIPT_NAME'] = script_name
            path_info = environ['PATH_INFO']
            if path_info.startswith(script_name):
                environ['PATH_INFO'] = path_info[len(script_name):]
        protocol = environ.get('HTTP_X_FORWARDED_PROTO', '')
        if protocol:
            environ['wsgi.url_scheme'] = protocol
        return self.app(environ, start_response)


def configure_logging(app):
    """
    Configures logging for Flask app
    """
    old_factory = logging.getLogRecordFactory()

    # noinspection PyBroadException
    def record_factory(*args, **kwargs):
        record = old_factory(*args, **kwargs)
        try:
            record.user = g.get('user_id') or 0
        except:  # noqa: E722
            record.user = 0
        return record

    logging.setLogRecordFactory(record_factory)
    logging.config.dictConfig(app.config["LOGGING"])


def user_id_from_headers(auth_header):
    """
    Extracts user ID from Authorization header, if available
    """
    if not auth_header:
        return None
    parts = auth_header.split('.')
    if len(parts) != 3:
        return None
    auth_data = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))
    return auth_data.get('user_claims', {}).get('user_id')
