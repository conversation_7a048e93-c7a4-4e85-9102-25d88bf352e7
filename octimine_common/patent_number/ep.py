import re
from typing import Pattern, AnyStr
from octimine_common.patent_number.base import PatentNumber


class _EPPublicationNumber(PatentNumber):

    NUMBER_PATTERN = re.compile("^[0-9]{4,7}$")

    def _build_number_wildcard(self) -> str:
        for i, char in enumerate(self.number):
            if char.isdigit():
                return self.number[:i] + '0*' + self.number[i:].lstrip('0')
        return self.number

    @classmethod
    def _get_number_pattern(cls) -> Pattern[AnyStr]:
        return cls.NUMBER_PATTERN
