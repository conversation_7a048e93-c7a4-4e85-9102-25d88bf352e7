from abc import ABC, abstractmethod
from typing import <PERSON><PERSON>, <PERSON><PERSON>, AnyStr
import re


class PatentNumber(ABC):

    def __init__(self, value: str):
        # extraction order matters
        self.authority_code, value = self._extract_authority_code(value)
        self.kind_code, value = self._extract_kind_code(value)
        self.number = self._extract_number(value)

    def join(self, by: str = '-') -> str:
        return self._join(self.authority_code, self.number, self.kind_code, by=by)

    def as_wildcard(self) -> str:
        authority_code_wildcard = self._build_authority_code_wildcard()
        number_wildcard = self._build_number_wildcard()
        kind_code_wildcard = self._build_kind_code_wildcard()
        return self._join(authority_code_wildcard, number_wildcard, kind_code_wildcard)

    @classmethod
    def _extract_authority_code(cls, value: str) -> Tuple[str, str]:
        if not value[:2].isalpha() or len(value[:2]) != 2:
            raise ValueError("Invalid patent number - missing/invalid authority code")
        return value[:2], value[2:]

    def _extract_number(self, value: str) -> str:
        if value.isalpha():
            raise ValueError('Invalid patent number - missing/invalid number')
        elif not self._get_number_pattern().match(value):
            raise ValueError('Invalid patent number - missing/invalid number')
        return value

    @classmethod
    @abstractmethod
    def _get_number_pattern(cls) -> Pattern[AnyStr]:
        """This should return a compiled pattern that validates numbers according to the authority"""
        pass

    @classmethod
    def _extract_kind_code(cls, value: str) -> Tuple[str, str]:
        if value[-1:].isalpha():  # Kind, one letter
            return value[-1:], value[:-1]
        elif value[-2:-1].isalpha() and value[-1].isdigit():  # Kind, letter and number
            return value[-2:], value[:-2]
        else:
            return '', value

    def _build_authority_code_wildcard(self) -> str:
        return self.authority_code

    @abstractmethod
    def _build_number_wildcard(self) -> str:
        pass

    def _build_kind_code_wildcard(self) -> str:
        return self.kind_code or '.{0,2}'

    @classmethod
    def _join(cls, *args, by: str = '-') -> str:
        return by.join([p for p in args if p])

    def is_design_patent(self) -> bool:
        if self.kind_code and re.match(r'S\d?$', self.kind_code):
            return True
        elif self.authority_code == 'UY' and self.kind_code == 'Q':  # Just be special, Uruguay
            return True
        elif not self.kind_code:
            if self.authority_code in ('US', 'ME', 'PH') and self.number.startswith('D'):
                return True
            elif self.authority_code == 'BR' and self.number.startswith('DI'):
                return True
        return False

    def is_utility_model(self) -> bool:
        rules = {
            'AR': ('A4', 'A5', 'A6', 'B4', 'B5', 'B6'),
            'BR': ('Z8', 'Z9'),
            'ES': ('X4',),
            'FR': ('A7', 'A8'),
            'HU': ('V0', ),
            'IT': ('V0', 'Z2'),
            'JP': ('Z1', 'Z2', 'I'),
            'MD': ('I1', 'I2', 'W1', 'W2', 'W8', 'W9'),
            'PE': ('Z',),
            'PH': ('Z',),
            'TR': ('T5', 'T6'),
        }
        if not self.kind_code:
            return False  # Can't guess without kind code
        if re.match(r'[U|Y]\d?$', self.kind_code):
            return True
        elif self.authority_code in rules and self.kind_code in rules[self.authority_code]:
            return True
        return False
