import re
from typing import Pattern, AnyStr
from octimine_common.patent_number.base import PatentNumber


class _KRPatentNumber(PatentNumber):

    NUMBER_PATTERN = re.compile("^[0-9]{4,13}$")

    def _build_number_wildcard(self) -> str:
        if self.number.startswith('0'):
            return '0' + self.number.lstrip('0')
        if len(self.number) == 13:  # KR Application numbers might start with 2 type digits in the EPO docs
            return self.number[2:]
        return self.number

    @classmethod
    def _get_number_pattern(cls) -> Pattern[AnyStr]:
        return cls.NUMBER_PATTERN
