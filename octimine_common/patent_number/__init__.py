from typing import Type
from octimine_common.patent_number.base import PatentNumber
from octimine_common.patent_number.br import _BRPublicationNumber
from octimine_common.patent_number.ch import _CHPublicationNumber
from octimine_common.patent_number.default import _DefaultPatentNumber
from octimine_common.patent_number.ep import _EPPublicationNumber
from octimine_common.patent_number.in_ import _INPublicationNumber
from octimine_common.patent_number.fr import _FRPublicationNumber
from octimine_common.patent_number.kr import _KRPatentNumber
from octimine_common.patent_number.wo import _WOPublicationNumber
from octimine_common.patent_number.us import _USPublicationNumber
from octimine_common.patent_number.de import _DEPublicationNumber
from octimine_common.patent_number.sg import _SGPatentNumber

_SUPPORTED_AUTHORITIES = {
  "AM", "AP", "AR", "AT", "AU", "AW",
  "BA", "BD", "BE", "BG", "BO", "BR", "BX", "BY",
  "CA", "CC", "CH", "CL", "CN", "CO", "CR", "CS", "CU", "CY", "CZ",
  "DD", "DE", "DK", "DO", "DZ", "EA",
  "EC", "EE", "EG", "EM", "EP", "ES",
  "FI", "FR",
  "GB", "GC", "GE", "GG", "GR", "GT",
  "HK", "HN", "HR", "HU",
  "ID", "IE", "IL", "IN", "IS", "IT",
  "JE", "JO", "JP",
  "KE", "KG", "KR", "KZ",
  "LT", "LU", "LV",
  "MA", "MC", "MD", "ME", "MN", "MO", "MT", "MW", "MX", "MY",
  "NI", "NL", "NO", "NZ",
  "OA",
  "PA", "PE", "PH", "PL", "PT", "PY",
  "RO", "RS", "RU",
  "SA", "SE", "SG", "SI", "SK", "SM", "SU", "SV",
  "TH", "TJ", "TN", "TR", "TT", "TW",
  "UA", "US", "UY", "UZ",
  "VE", "VN",
  "WO",
  "YU",
  "ZA", "ZM",
}


class BasePatentNumberParser:
    _authority_handlers = {}

    @classmethod
    def get_authority_handler(cls, authority: str) -> Type[PatentNumber]:
        """ Returns the parser class that handles patent numbers for the incoming authority
        :param authority: The authority being used for getting a parser implementation
        :return: The parser class that handles patent numbers for the incoming authority
        """
        if authority not in _SUPPORTED_AUTHORITIES:
            raise ValueError('Authority not supported')
        if authority in cls._authority_handlers:
            return cls._authority_handlers[authority]
        else:
            return _DefaultPatentNumber

    @classmethod
    def parse(cls, patent_number: str) -> PatentNumber:
        """ Parses a patent number using the most appropriate parser based on authority.
        :param patent_number: The patent number string to be parsed
        :return: PatentNumber instance that represents the string
        """
        patent_number = cls._normalize(patent_number)
        return cls.get_authority_handler(patent_number[:2])(patent_number)

    @classmethod
    def _normalize(cls, value: str) -> str:
        return value.replace('-', '').replace('/', '').upper()


class PatentNumberParser:

    @classmethod
    def parse(cls, patent_number: str) -> PatentNumber:
        """ Parses a patent number using all available parsers.
        :param patent_number: The patent number string to be parsed
        :return: PatentNumber instance that best represents the string
        """
        # application numbers are at last since they have very generic pattern
        for parser in (PublicationNumberParser, ApplicationNumberParser):
            try:
                return parser.parse(patent_number)
            except ValueError:
                pass
        raise ValueError('Invalid patent number - did not match any parser')


class PublicationNumberParser(BasePatentNumberParser):

    _authority_handlers = {
        "EP": _EPPublicationNumber,
        "WO": _WOPublicationNumber,
        "US": _USPublicationNumber,
        "DE": _DEPublicationNumber,
        "FR": _FRPublicationNumber,
        "CA": _FRPublicationNumber,  # Canada and Spain use a numbering system very similar to France
        "ES": _FRPublicationNumber,
        "KR": _KRPatentNumber,
        "CH": _CHPublicationNumber,
        "BR": _BRPublicationNumber,
        "IN": _INPublicationNumber,
        "SG": _SGPatentNumber,
    }


class ApplicationNumberParser(BasePatentNumberParser):

    _authority_handlers = {
        "SG": _SGPatentNumber,
        "KR": _KRPatentNumber
    }
