import re
from typing import Pattern, AnyStr
from octimine_common.patent_number.base import PatentNumber


class _USPublicationNumber(PatentNumber):

    NUMBER_PATTERN = re.compile("^[A-Z]{0,2}[0-9]+$")

    def _build_number_wildcard(self):
        if self.number[:4].isdigit() and 2000 <= int(self.number[:4]) <= 2099:
            return self.number[:4] + '0*' + self.number[4:].lstrip('0')
        elif self.number[:2] == 'RE' or self.number[:2] == 'PP':
            return self.number[:2] + '0*' + self.number[2:].lstrip('0')
        elif self.number[:1] == 'D':
            return self.number[:1] + '0*' + self.number[1:].lstrip('0')
        else:
            return self.number

    @classmethod
    def _get_number_pattern(cls) -> Pattern[AnyStr]:
        return cls.NUMBER_PATTERN
