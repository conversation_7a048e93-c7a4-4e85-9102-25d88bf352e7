import re
from typing import Pattern, AnyStr
from octimine_common.patent_number.base import PatentNumber


class _CHPublicationNumber(PatentNumber):

    NUMBER_PATTERN = re.compile("^[0-9]{1,10}[A-Z]?$")

    def _build_number_wildcard(self) -> str:
        if self.number.startswith('00'):
            return self.number.lstrip('0')
        return self.number

    @classmethod
    def _get_number_pattern(cls) -> Pattern[AnyStr]:
        return cls.NUMBER_PATTERN
