import re
from typing import Pattern, AnyStr
from octimine_common.patent_number.base import PatentNumber


class _WOPublicationNumber(PatentNumber):

    NUMBER_PATTERN = re.compile("^[0-9]{1,10}$")

    def _build_number_wildcard(self):
        pattern = self.number
        if len(pattern) == 8 and pattern[0] in ('0', '1'):
            pattern = '20' + pattern
        if pattern[0] == '2' or pattern[0] == '1':  # starts with 1 or 2, already 4 digit year
            return pattern[:4] + '0*' + pattern[4:].lstrip('0')
        elif pattern[0] == '0':  # two-digit year between 2000 and 2004
            return '20' + pattern[:2] + '0*' + pattern[2:].lstrip('0')
        else:  # two-digit year between 1974 and 1999
            return '19' + pattern[:2] + '0*' + pattern[2:].lstrip('0')

    @classmethod
    def _get_number_pattern(cls) -> Pattern[AnyStr]:
        return cls.NUMBER_PATTERN
