from dataclasses import dataclass, asdict
from typing import Dict, List, Optional
from flask import Request, current_app
from flask_jwt_extended import get_jwt_identity

from app.models.subscription_group import SubscriptionGroup, PREMIUM
from octimine_common.enums import SubscriptionType
from octimine_common.jwt_utils import get_jwt_claims


@dataclass(frozen=True)
class Requester:
    """ Wraps all user related information
    """

    user_id: int
    username: str
    search_quota: int
    access_throttle: int
    max_results: int
    subscription_type: str
    features: List[str]
    is_admin: bool = False
    is_manager: bool = False
    is_sales: bool = False
    ipl_demo_user: bool = False
    company_id: Optional[int] = None
    locale: Optional[str] = None
    max_monitor_profile: Optional[int] = None

    @staticmethod
    def from_dict(data: Dict) -> 'Requester':
        return Requester(**data)

    @staticmethod
    def from_request(request: Request) -> 'Requester':
        """
        Build a Requester object from current http request
        """
        jwt_claims = get_jwt_claims()
        identity = get_jwt_identity()
        return Requester(
            user_id=jwt_claims.get("user_id"),
            username=identity,
            is_admin=jwt_claims.get("is_admin"),
            is_manager=jwt_claims.get("is_manager"),
            is_sales=jwt_claims.get("is_sales"),
            search_quota=jwt_claims.get("sq"),
            access_throttle=jwt_claims.get("at"),
            max_results=jwt_claims.get("mr"),
            max_monitor_profile=jwt_claims.get('mp'),
            subscription_type=jwt_claims.get("st"),
            features=jwt_claims.get('ft') or [],
            ipl_demo_user=jwt_claims.get('ipl_demo_user'),
            company_id=jwt_claims.get("company_id"),
            locale=(
                request.accept_languages.best_match(current_app.config.get('AVAILABLE_LOCALES', [])) or
                jwt_claims.get('locale')
            )
        )

    def to_dict(self):
        return asdict(self)

    def has_free_subscription(self):
        return self.subscription_type == 'FREE'

    def has_collaborator_subscription(self):
        return self.subscription_type == 'COLLABORATOR'

    def has_premium_subscription(self):
        return SubscriptionGroup.from_type(SubscriptionType.from_id(self.subscription_type)) is PREMIUM
