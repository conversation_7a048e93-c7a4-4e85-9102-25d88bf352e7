import iso3166


# Here we can control such authorities for which we need special names
_AUTHORITIES = {
    'EP': 'EPO',
    'WO': 'WIPO',
    'US': 'USA',
    'GB': 'United Kingdom'
}


def get_authority_name(authority_code: str):
    if authority_code in _AUTHORITIES:
        return _AUTHORITIES.get(authority_code)
    elif authority_code in iso3166.countries:
        country = iso3166.countries.get(authority_code)
        if ',' in country.name:
            return country.name.split(',')[0]  # i.e. To get "Korea" instead of "Korea, Republic of"
        return country.name
    return authority_code
