from http import HTTPStatus
from . import messages

errors = {
    'BadRequest': {
        'message': messages.ERROR_BAD_REQUEST,
        'status': HTTPStatus.BAD_REQUEST
    },
    'Unauthorized': {
        'message': messages.ERROR_INVALID_CREDENTIALS,
        'status': HTTPStatus.UNAUTHORIZED
    },
    'InternalServerError': {
        'message': messages.ERROR_INTERNAL_SERVER_ERROR,
        'status': HTTPStatus.INTERNAL_SERVER_ERROR
    },
    'MethodNotAllowed': {
        'message': messages.ERROR_METHOD_NOT_ALLOWED,
        'status': HTTPStatus.METHOD_NOT_ALLOWED
    },
    'NotFound': {
        'message': messages.ERROR_RESOURCE_NOT_FOUND,
        'status': HTTPStatus.NOT_FOUND
    },
    'Forbidden': {
        'message': messages.ERROR_FORBIDDEN,
        'status': HTTPStatus.FORBIDDEN
    },
    'ServiceUnavailable': {
        'message': messages.ERROR_SERVICE_UNAVAILABLE,
        'status': HTTPStatus.SERVICE_UNAVAILABLE
    }
}
