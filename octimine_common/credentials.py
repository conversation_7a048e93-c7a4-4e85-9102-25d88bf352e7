import re
from dataclasses import dataclass, asdict
from typing import Dict, Optional
from flask import Request


@dataclass
class Credentials:
    """ Wraps credentials information
    """

    access_token: str
    refresh_token: Optional[str] = None

    @staticmethod
    def from_dict(data: Dict) -> 'Credentials':
        return Credentials(**data)

    @staticmethod
    def from_request(request: Request) -> 'Credentials':
        """
        Build a Credentials object from current http request
        """
        return Credentials(
            access_token=get_access_token(request),
            refresh_token=get_refresh_token(request)
        )

    def to_dict(self):
        return asdict(self)


def get_access_token(request: Request) -> Optional[str]:
    auth_header = request.headers.get('Authorization')
    return get_bearer_token(auth_header)


def get_refresh_token(request: Request) -> Optional[str]:
    auth_header = request.headers.get('Refresh')
    return get_bearer_token(auth_header)


def get_bearer_token(auth_header):
    if not auth_header:
        return None
    values = re.split(r"\s+", auth_header)
    if len(values) != 2:
        return None
    _, access_token = values
    return access_token
