import traceback
from flask import make_response, jsonify, current_app
from marshmallow import ValidationError
from http import HTTPStatus


class APIErrorHandler:
    _logger = None

    def __init__(self, app=None):
        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        self._logger = app.logger

    def _debug(self):
        return current_app.config['DEBUG']

    def _traceback(self):
        return {'traceback': traceback.format_exc()}

    def handle_validation_error(self, e: ValidationError):
        self._logger.warning('Validation error: %s', e)
        return make_response(jsonify({
            "status": HTTPStatus.BAD_REQUEST,
            "message": "Validation error: " + str(e),
            "details": e.normalized_messages()
        }), HTTPStatus.BAD_REQUEST)

    def handle_json_error(self, e):
        self._logger.warning('JSON parsing error: %s', e)
        return make_response(jsonify({"status": HTTPStatus.BAD_REQUEST, "message": "JSON error: " + str(e)}),
                             HTTPStatus.BAD_REQUEST)

    def handle_api_error(self, e):
        self._logger.log(e.get_log_level(), e)
        response = {"status": e.code, "message": str(e.message)}
        response.update(e.get_additional_data())
        response.update(self._traceback() if self._debug() else {})
        return make_response(jsonify(response), e.code)

    def handle_elastic_search_api_error(self, e):
        return make_response(jsonify({"status": e.meta.status, "message": "Elastic search error: " + str(e)}),
                             HTTPStatus.INTERNAL_SERVER_ERROR)

    def handle_elastic_search_transport_error(self, e):
        return make_response(jsonify({
            "status": HTTPStatus.INTERNAL_SERVER_ERROR,
            "message": "Elastic search error: " + str(e)
        }), HTTPStatus.INTERNAL_SERVER_ERROR)

    def handle_http_error(self, e):
        return make_response(jsonify({"status": e.code, "message": e.description}), e.code)

    def handle_rate_limit_error(self, e):
        return make_response(jsonify({"status": HTTPStatus.TOO_MANY_REQUESTS, "message": e.message}),
                             HTTPStatus.TOO_MANY_REQUESTS)

    def handle_generic_error(self, e):
        self._logger.exception(e)
        response = {
            "status": HTTPStatus.INTERNAL_SERVER_ERROR,
            "message": "Internal server error: " + str(e)
        }
        response.update(self._traceback() if self._debug() else {})
        return make_response(jsonify(response), HTTPStatus.INTERNAL_SERVER_ERROR)
