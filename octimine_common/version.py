import os
import re


def read_version(current_app):
    """
    Helper function to read the version information from current app's setup.py file
    """
    version_re = r"['\"](\d+\.\d+\.\d+)['\"]"
    with open(os.path.join(current_app.instance_path, '..', 'setup.py'), 'r') as f:
        for line in f.readlines():
            if 'version' in line:
                match = re.search(version_re, line)
                if match:
                    return match.group(1)
    return None
