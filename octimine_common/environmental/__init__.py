import json
import os
import re
from collections import defaultdict
from typing import Set, Dict, Optional, Tuple, List


def is_any_environmental(classification_codes: Set[str]) -> bool:
    return len(get_environmental_classification_codes(classification_codes)) > 0


def get_environmental_classification_codes(classification_codes: Set[str]) -> Set[str]:
    return classification_codes.intersection(ENVIRONMENTAL_CLASSIFICATION_CODES)


def get_environmental_classification_codes_and_categories(classification_codes: Set[str]) -> List[dict]:
    result = list()
    green_codes = get_environmental_classification_codes(classification_codes)
    if len(green_codes) > 0:
        for code in green_codes:
            result.append({code: ENVIRONMENTAL_CATEGORIES[code]})
    return result


def _clean_category_title(title: Optional[str]) -> Optional[str]:
    return re.sub(r'^([0-9]+[.]*)+[\s]*|^([^a-zA-Z0-9]+)', '', title).strip() if title else None


def _load_environmental_data() -> Tuple[Set[str], Dict]:
    directory = os.path.dirname(__file__)
    file = os.path.join(directory, 'environmental_categories.json')
    with open(file, 'r') as f:
        return _extract_classification_codes_and_categories(json.loads(f.read()), None, None)


def _extract_classification_codes_and_categories(environmental_categories: Dict,
                                                 root_title: Optional[str],
                                                 parent_title: Optional[str]) -> Tuple[Set[str], Dict]:
    values = set()
    categories = defaultdict(dict)
    for key, value in environmental_categories.items():
        valid_root_title = root_title if root_title else _clean_category_title(key)
        if isinstance(value, Dict):
            valid_parent_title = _clean_category_title(key) if len(key) > 0 else parent_title
            _values, _categories = _extract_classification_codes_and_categories(value, valid_root_title,
                                                                                valid_parent_title)
            categories.update(_categories)
            values.update(_values)
        elif isinstance(value, str):
            cpc_codes = re.findall(r'[A-Z][A-Z0-9]{0,5}(?:/[0-9]+)?', value)
            values.update(cpc_codes)
            for cpc in cpc_codes:
                categories[cpc] = {
                    'root_title': valid_root_title,
                    'parent_title': parent_title,
                    'title': _clean_category_title(key)
                }
    return values, categories


ENVIRONMENTAL_CLASSIFICATION_CODES, ENVIRONMENTAL_CATEGORIES = _load_environmental_data()
