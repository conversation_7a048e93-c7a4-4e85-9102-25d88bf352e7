from typing import List, <PERSON><PERSON>
from marshmallow import ValidationError, Schema
from octimine_common.fields import PatentNumberField, PublicationNumberField, ApplicationNumberField


class _PatentNumberSchema(Schema):
    input = PatentNumberField(required=True)


class _ApplicationNumberSchema(Schema):
    input = ApplicationNumberField(required=True)


class _PublicationNumberSchema(Schema):
    input = PublicationNumberField(required=True)


class PatentNumberValidator:

    _schema = _PatentNumberSchema()

    @classmethod
    def validate_many(cls, numbers: List[str]) -> Tuple[List[str], List[str]]:
        """
        Validates a list of patent numbers

        :param numbers: The numbers to validate
        :return: A tuple with the list of valid and invalid numbers from the input list
        """
        valid, invalid = [], []
        for number in numbers:
            try:
                cls.validate(number)
                valid.append(number)
            except ValidationError:
                invalid.append(number)
        return valid, invalid

    @classmethod
    def validate(cls, number: str):
        """Validates one single number"""
        cls._schema.load({'input': number})


class PublicationNumberValidator(PatentNumberValidator):
    _schema = _PublicationNumberSchema()


class ApplicationNumberValidator(PatentNumberValidator):
    _schema = _ApplicationNumberSchema()
