from marshmallow import fields, ValidationError
from babel.dates import format_date
from datetime import date
from octimine_common.patent_number import PatentNumberParser, ApplicationNumberParser, PublicationNumberParser, \
    BasePatentNumberParser


class BasePatentNumberField(fields.Field):
    """
    Custom field to validate various patent number fields
    """
    number_parser = BasePatentNumberParser

    @classmethod
    def get_patent_number_wildcard(cls, patent_number: str):
        if not patent_number:
            return patent_number
        patent_number = cls._parse(patent_number)
        return '/' + patent_number.as_wildcard() + '/'

    @staticmethod
    def get_presentation_value(patent_number: str):
        if not patent_number:
            return patent_number
        return patent_number.replace('-', '')

    def _serialize(self, value, attr, obj, **kwargs):
        if value is None:
            return None
        return value.replace('-', '')

    def _deserialize(self, value, attr, data, **kwargs):
        if value is None:
            return None
        patent_number = self._parse(value)
        return patent_number.join()

    @classmethod
    def _parse(cls, patent_number: str):
        try:
            patent_number = cls.number_parser.parse(patent_number)
            return patent_number
        except ValueError as e:
            raise ValidationError(str(e))


class PatentNumberField(BasePatentNumberField):
    """
    Custom field to validate patent numbers of any kind.
    """
    number_parser = PatentNumberParser


class PublicationNumberField(BasePatentNumberField):
    """
    Custom field to validate publication numbers according to the international format
    """
    number_parser = PublicationNumberParser


class ApplicationNumberField(BasePatentNumberField):
    """
    Custom field to validate application numbers according to the international format
    TODO: currently performs only very basic checks. Extend to perform more advanced checks in the future
    """
    number_parser = ApplicationNumberParser


class LDMLDateFormatField(fields.Field):

    def _serialize(self, value, attr, obj, **kwargs):
        self._parse(value)
        return value

    def _deserialize(self, value, attr, data, **kwargs):
        if value is None:
            return None
        self._parse(value)
        return value

    @staticmethod
    def _parse(value):
        try:
            formatted = format_date(date.today(), value)
            if formatted == value:
                raise ValidationError("Provided LDML date formatting pattern has no date fields")
        except AttributeError:
            raise ValidationError("Provided LDML date formatting pattern is not valid")
