# JWT
from octimine_common.app_request import AppRequest
from octimine_common.credentials import Credentials
from octimine_common.requester import Requester
from octimine_common.request_ctx import RequestContext

access_token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE1NzE3MjkyMTYsIm5iZiI6MTU3MTcyOTIxNiwianRpIjoiNWQ3ZTJh' \
               'ZjQtN2I5YS00NzUzLTkxODItNzdkN2YxM2UzNGQ1IiwiZXhwIjoxNTcxNzUwODE2LCJzdWIiOiJJbnRlZ3JhdGlvbiBUZXN0Iiwia' \
               'WRlbnRpdHkiOiJJbnRlZ3JhdGlvbiBUZXN0IiwiZnJlc2giOmZhbHNlLCJ0eXBlIjoiYWNjZXNzIiwidXNlcl9jbGFpbXMiOnsidX' \
               'Nlcl9pZCI6MSwic3QiOiJQUk9GRVNTSU9OQUwiLCJtcCI6MjB9fQ._dcCdlOY_eKQUkiYtx-Jl2QLyIp5kOZ8TGdKferyvLg'
refresh_token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE1NTExMTg3NTgsIm5iZiI6MTU1MTExODc1OCwianRpIjoiMWE4NDN' \
                'jZjktNTQ5Mi00OWNjLWE3YmEtZWQzOTg4NDAyNTY3IiwiZXhwIjoxNTUzNzEwNzU4LCJzdWIiOiJJbnRlZ3JhdGlvbiBUZXN0Iiw' \
                'iaWRlbnRpdHkiOiJJbnRlZ3JhdGlvbiBUZXN0IiwiZnJlc2giOmZhbHNlLCJ0eXBlIjoicmVmcmVzaCIsInVzZXJfY2xhaW1zIjp' \
                '7InVzZXJfaWQiOjEsInN0IjoiUFJPRkVTU0lPTkFMIiwibXAiOjIwfX0.XkKK4RIl734wdUmV1dLnComPWLn1wuqit1Ky8D37XGQ'
manager_access_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTY5MDkwMzA2NywianRpIjoiNmM1' \
                       'YWFhOTMtODA3Ni00ZTE4LWE4MDYtNmFlNzhiNWI1ZTc1IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5IjoiZW1haWxAZ' \
                       'XhhbXBsZS5jb20iLCJuYmYiOjE2OTA5MDMwNjcsImV4cCI6MjAzNjUwMzA2NywidXNlcl9jbGFpbXMiOnsic2giOm51bG' \
                       'wsInVzZXJfaWQiOjIsInR5cGUiOiJhcHBfdXNlciIsImlzX2FkbWluIjpudWxsLCJpc19zYWxlcyI6bnVsbCwiaXNfbWF' \
                       'uYWdlciI6dHJ1ZSwiY29tcGFueV9pZCI6MSwiZGVsZWdhdGlvbl9zb3VyY2UiOm51bGwsImxvY2FsZSI6bnVsbCwic3Ei' \
                       'OjIsInJzcSI6MCwiYXQiOjUsIm1yIjoyNSwibXAiOm51bGwsInN0IjoiRlJFRSIsImZ0IjpbXX19.vgo2tKvd_WhqxtWw' \
                       'DxS5KQZRpbtWQ0slWCRh79j4p9c'
manager_access_jti = '6c5aaa93-8076-4e18-a806-6ae78b5b5e75'
access_jti = '5d7e2af4-7b9a-4753-9182-77d7f13e34d5'
refresh_jti = '1a843cf9-5492-49cc-a7ba-ed3988402567'


def get_app_request(user_id=1, token=access_token, features=None, subscription_type="ENTERPRISE",
                    is_admin=False, is_sales=False, is_manager=False):
    return AppRequest(requester=Requester(user_id=user_id, username="test", company_id=1, search_quota=100,
                                          access_throttle=100, max_results=100, max_monitor_profile=20,
                                          is_admin=is_admin, is_sales=is_sales, is_manager=is_manager,
                                          subscription_type=subscription_type, features=features or []),
                      credentials=Credentials(access_token=token), payload=dict())


def get_request_ctx(user_id=1, company_id=1, token=access_token, features=None, subscription_type="FREE",
                    is_admin=False, is_sales=False, is_manager=False):
    return RequestContext(requester=Requester(user_id=user_id, username="test", company_id=company_id, search_quota=100,
                                              access_throttle=100, max_results=100, max_monitor_profile=20,
                                              is_admin=is_admin, is_sales=is_sales, is_manager=is_manager,
                                              subscription_type=subscription_type, features=features or []),
                          credentials=Credentials(access_token=token))
