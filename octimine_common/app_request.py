from dataclasses import dataclass, asdict, replace
from typing import Dict
from flask import Request
from octimine_common.credentials import Credentials
from octimine_common.request_ctx import ctx
from octimine_common.requester import Requester


@dataclass(frozen=True)
class AppRequest:
    requester: Requester
    credentials: Credentials
    payload: Dict
    internal: bool = False

    @staticmethod
    def from_request(request: Request) -> 'AppRequest':
        """
        Build a AppRequest object from current http request.
        The payload can be passed as a custom Dict instead of from http request.
        """
        data = request.get_json(force=True) if request.data else {}
        if isinstance(data, list):
            payload = {'items': data}
        elif isinstance(data, dict):
            payload = data
        else:
            payload = {}
        return AppRequest(
            requester=Requester.from_request(request),
            credentials=Credentials.from_request(request),
            payload=payload,
            internal='X-Octimine-Internal' in request.headers
        )

    @staticmethod
    def from_dict(data: Dict) -> 'AppRequest':
        return AppRequest(
            requester=Requester.from_dict(data.get('requester')),
            credentials=Credentials.from_dict(data.get('credentials')),
            payload=data.get('payload', {}),
            internal=data.get('internal') or False
        )

    @staticmethod
    def from_ctx():
        return AppRequest(
            requester=ctx.requester,
            credentials=ctx.credentials,
            payload={},
            internal=ctx.internal
        )

    def to_dict(self):
        return asdict(self)

    def replace(self, **changes) -> 'AppRequest':
        """
        Return a new object replacing specified fields with new values.
        """
        return replace(self, **changes)

    def is_internal(self):
        return self.internal

    def is_external(self):
        return not self.internal
