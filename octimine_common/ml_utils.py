import os
from typing import List
from app.ext.machine_learning.aws import write_dataset_to_ndjson
from app.ext.machine_learning.client import DatasetParams, DatasetInfo

from app.services.monitor.machine_learning.dataset import DatasetPreparer
from app.models.monitor.machine_learning import MachineLearningDatasetEntryModel
from app.models.monitor.monitor import MonitorProfileModel

from octimine_common.app_request import AppRequest
from octimine_common.requester import Requester
from octimine_common.credentials import Credentials


def _create_intermediate_folders(file_path: str) -> None:
    """Create intermediate folders for a given file_path if they don't exist.

    Args:
        file_path (str): The path of the file.
    """
    directory = os.path.dirname(file_path)
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created directory: {directory}")
    else:
        print(f"Directory already exists: {directory}")


def _create_dataset_local(params: DatasetParams, local_folder: str = "_local_output") -> DatasetInfo:
    """Create a local dataset.

    Args:
        params (DatasetParams): The parameters for creating the dataset.
        local_folder (str, optional): The local folder to store the dataset. Defaults to "_local_output".

    Returns:
        DatasetInfo: The information of the created dataset.
    """
    filename = params.internal_id
    object_name = f"{local_folder}/{filename}/dataset.json"

    _create_intermediate_folders(object_name)

    write_dataset_to_ndjson(object_name, params.examples)
    return DatasetInfo(details=dict())


def generate_training_data(profile_ids: List[int], credentials: Credentials) -> None:
    """Generate training data for the given profile IDs.

    Args:
        profile_ids (List[int]): The list of profile IDs.
        credentials (Credentials): The credentials for generating the training data.
    """
    for profile_id in profile_ids:
        try:
            profile: MonitorProfileModel = MonitorProfileModel.query.filter_by(id=profile_id).one()
            app_req = AppRequest(
                requester=Requester(
                    user_id=profile.user_id,
                    username="",
                    search_quota=1000,
                    access_throttle=1000,
                    max_results=1000,
                    max_monitor_profile=1000,
                    is_admin=True,
                    company_id=None,
                    features=['L', 'M'],
                    subscription_type="ENTERPRISE"
                ),
                credentials=credentials,
                payload={}
            )

            internal_id = str(profile_id)
            dataset_preparer = DatasetPreparer(
                app_req,
                MachineLearningDatasetEntryModel.query.filter_by(
                    profile_id=profile_id
                ).all()
            )
            dataset = dataset_preparer.exec()

            _create_dataset_local(DatasetParams(internal_id=internal_id, examples=dataset.examples))
        except Exception:
            print(f"Failed read for profile {profile_id}")

# Example on how to utilize these functions in a standalone script https://gitlab.com/-/snippets/3636327
