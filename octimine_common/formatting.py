from typing import Optional

from babel.core import UnknownLocaleError
from babel.dates import format_date as _format_date
from datetime import date


def _normalize_locale(locale):
    return locale.replace('-', '_') if locale else None


def format_date_str(isodate: Optional[str], *, locale: str = None, date_format: str = None) -> str:
    try:
        date_obj = date.fromisoformat(isodate) if isodate else None
    except ValueError:
        return isodate

    return format_date(date_obj, locale=locale, date_format=date_format)


def format_date(date_obj: Optional[date], *, locale: str = None, date_format: str = None) -> str:
    if not date_obj:
        return ''
    try:
        locale = _normalize_locale(locale) or 'en'
        date_format = date_format or 'short'
        return _format_date(date_obj, locale=locale, format=date_format)
    except (AttributeError, UnknownLocaleError):
        return date_obj.isoformat()
