from typing import Callable
from flask import current_app
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from octimine_common.credentials import Credentials


def authenticate_system_user(authenticate: Callable[[str, str], dict]) -> Credentials:
    if not current_app.config.get('SYSTEM_USER_API_KEY') or not current_app.config.get('SYSTEM_USER_RSA_PRIVATE_KEY'):
        raise ValueError("Cannot authenticate system user. Missing configuration entries")
    api_key = current_app.config['SYSTEM_USER_API_KEY']
    private_key = serialization.load_pem_private_key(
        current_app.config['SYSTEM_USER_RSA_PRIVATE_KEY'].encode(),
        password=None, backend=default_backend()
    )
    pss_padding = padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=32)
    signature = private_key.sign(api_key.encode(), pss_padding, hashes.SHA256()).hex()
    auth_tokens = authenticate(api_key, signature)
    return Credentials(access_token=auth_tokens['access_token'], refresh_token=auth_tokens['refresh_token'])
