from contextlib import suppress
from flask import g, Request, request
from flask_jwt_extended import decode_token
from werkzeug.local import LocalProxy
from dataclasses import dataclass, asdict
from octimine_common.exceptions import ForbiddenException
from octimine_common.requester import Requester
from octimine_common.credentials import Credentials


@dataclass
class RequestContext:
    requester: Requester
    credentials: Credentials
    internal: bool = False

    @staticmethod
    def from_request(request: Request) -> 'RequestContext':
        return RequestContext(
            requester=Requester.from_request(request),
            credentials=Credentials.from_request(request),
            internal='X-Octimine-Internal' in request.headers
        )

    @staticmethod
    def from_dict(data: dict) -> 'RequestContext | None':
        requester = data.get('requester')
        credentials = data.get('credentials')
        if not requester or not credentials:
            return
        return RequestContext(
            requester=Requester.from_dict(requester),
            credentials=Credentials.from_dict(credentials),
            internal=data.get('internal') or False
        )

    @staticmethod
    def from_credentials(credentials: Credentials):
        decoded_token = decode_token(encoded_token=credentials.access_token)
        claims = decoded_token.get('user_claims', {})
        return RequestContext(
            requester=Requester(
                user_id=claims.get('user_id'),
                username=decoded_token.get('identity', ""),
                search_quota=claims.get('sq'),
                access_throttle=claims.get('at'),
                max_results=claims.get('mr'),
                max_monitor_profile=claims.get('mp'),
                is_admin=claims.get('is_admin'),
                company_id=claims.get('company_id'),
                features=claims.get('ft'),
                subscription_type=claims.get('st'),
                locale=claims.get('locale')
            ),
            credentials=credentials,
        )

    def to_dict(self):
        return asdict(self)


def set_ctx(request_ctx: RequestContext | None):
    g.request_ctx = request_ctx


def get_optional_ctx() -> RequestContext | None:
    if 'request_ctx' in g:
        return g.request_ctx

    with suppress(RuntimeError):
        ctx = RequestContext.from_request(request)
        set_ctx(ctx)
        return ctx
    return None


def get_ctx() -> RequestContext:
    ctx = get_optional_ctx()
    if not ctx:
        raise ForbiddenException('Operation not permited without request context')
    return ctx


ctx: RequestContext = LocalProxy(get_ctx)
optional_ctx: RequestContext | None = LocalProxy(get_optional_ctx)
