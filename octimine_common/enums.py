from enum import Enum, auto


class SearchType(Enum):
    SEMANTIC = 'SEMANTIC'
    CITATION = 'CITATION'
    BOOLEAN = 'BOOLEAN'
    PATENT_NUMBER = 'PATENT_NUMBER'
    DOCUMENT_ID = 'DOCUMENT_ID'
    NPL = 'NPL'

    def __str__(self):
        return self.name

    @classmethod
    def from_hash(cls, search_hash: str):
        if not search_hash:
            return None
        elif search_hash.startswith('s'):
            return cls.SEMANTIC
        elif search_hash.startswith('b'):
            return cls.BOOLEAN
        elif search_hash.startswith('c'):
            return cls.CITATION
        elif search_hash.startswith('p'):
            return cls.PATENT_NUMBER
        elif search_hash.startswith('d'):
            return cls.DOCUMENT_ID
        return None


class ESDocumentType(str, Enum):
    FAMILY = "biblytics"
    PUBLICATION = "doc_info"
    PAPER = "semantic_scholar"

    def __str__(self):
        return self.name


class SubscriptionType(Enum):
    FREE = auto()
    TRIAL = auto()
    BASIC = auto()
    COLLABORATOR = auto()
    PROFESSIONAL = auto()
    ENTERPRISE = auto()

    @classmethod
    def from_id(cls, plan_id: str):
        if not plan_id:
            return cls.FREE
        elif plan_id.lower().startswith('trial'):
            return cls.TRIAL
        elif plan_id.lower().startswith('basic'):
            return cls.BASIC
        elif plan_id.lower().startswith('collaborator'):
            return cls.COLLABORATOR
        elif plan_id.lower().startswith('professional'):
            return cls.PROFESSIONAL
        elif plan_id.lower().startswith('enterprise'):
            return cls.ENTERPRISE
        else:
            return cls.FREE

    def __str__(self):
        return self.name
