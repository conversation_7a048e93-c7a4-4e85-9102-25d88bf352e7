from flask import make_response, jsonify
from http import HTTPStatus
from . import messages


class JWTErrorHandler:
    @classmethod
    def invalid_token_error_handler(cls):
        response = dict(message=messages.ERROR_INVALID_TOKEN, status=HTTPStatus.UNAUTHORIZED)
        return make_response(jsonify(response), response['status'])

    @classmethod
    def expired_token_error_handler(cls):
        response = dict(message=messages.ERROR_EXPIRED_TOKEN, status=HTTPStatus.UNAUTHORIZED)
        return make_response(jsonify(response), response['status'])

    @classmethod
    def revoked_token_error_handler(cls):
        response = dict(message=messages.ERROR_REVOKED_TOKEN, status=HTTPStatus.UNAUTHORIZED)
        return make_response(jsonify(response), response['status'])
