import math
import logging
import tik<PERSON><PERSON>
import openai
from flask import current_app


_logger = logging.getLogger(__name__)


def create_client():
    return openai.AzureOpenAI(
        azure_endpoint=current_app.config['OPENAI_API_BASE'],
        api_key=current_app.config['OPENAI_API_KEY'],
        api_version="2024-08-01-preview",
    )


def _create_chat_completion(client, messages, temperature=0.1, stream=False):
    try:
        response = client.chat.completions.create(
            model=current_app.config['OPENAI_MODEL'],
            messages=messages,
            temperature=temperature,
            frequency_penalty=1,
            presence_penalty=1,
            seed=42,
            stream=stream
        )
        current_app.logger.debug(f"received response from OpenAI: {response}")
        return response
    except openai.OpenAIError:
        current_app.logger.exception("Error calling OpenAI APIs")
        raise


def chat_completions(client, messages, temperature=0.1) -> str:
    response = _create_chat_completion(client, messages, temperature, stream=False)
    if not response.choices or not response.choices[0].message.content:
        return ""
    return response.choices[0].message.content


def chat_completions_stream(client, messages, temperature=0.1):
    return _create_chat_completion(client, messages, temperature, stream=True)


def check_and_adjust_token_limits(messages_builder, docs, shrink_fields):
    messages = messages_builder()

    for sf in shrink_fields:
        max_tokens = current_app.config['OPENAI_MAX_TOKENS']
        used_tokens = num_tokens_from_messages(messages)
        _logger.debug(f'used tokens: {used_tokens}')
        available_tokens = max_tokens - used_tokens

        if available_tokens < 0 and docs:
            excess_tokens = abs(available_tokens)
            _logger.debug(f'not enough available tokens... {excess_tokens} tokens above limits')
            _logger.debug(f'shrinking tokens in field: {sf}')

            reduction_strategies = [
                ("conservative", 1.0),
                ("moderate", 2.0),
                ("aggressive", 0.5),
                ("drastic", 0.2)
            ]

            encoding = _get_token_encoding()

            for strategy_name, factor in reduction_strategies:
                if available_tokens >= 0:
                    break

                _logger.debug(f'Trying {strategy_name} reduction strategy for field: {sf}')

                for d in docs:
                    if d.get(sf):
                        field_text = d[sf]
                        current_tokens = len(encoding.encode(field_text))

                        if factor < 1.0:
                            target_tokens = int(current_tokens * factor)
                            _logger.debug(f'Reducing {sf} to {int(factor*100)}% of original size')
                        else:
                            tokens_to_remove = math.ceil(excess_tokens * factor / len(docs))
                            target_tokens = max(100, current_tokens - tokens_to_remove)
                            _logger.debug(f'Removing {tokens_to_remove} tokens from {sf}')

                        d[sf] = truncate_text_by_tokens(field_text, target_tokens)

                _logger.debug('rebuilding messages...')
                messages = messages_builder()
                used_tokens = num_tokens_from_messages(messages)
                available_tokens = max_tokens - used_tokens

                _logger.debug(f'{strategy_name} reduction result: {used_tokens} tokens (available: {available_tokens})')
                _logger.debug('done rebuilding messages')

                if available_tokens >= 0:
                    break

    return messages


def num_tokens_from_messages(messages):
    """
    Return the number of tokens used by a list of messages.
    Ref:
        - https://github.com/openai/openai-cookbook/blob/main/examples/How_to_count_tokens_with_tiktoken.ipynb
        - https://platform.openai.com/docs/models/gpt-4o       -
    """
    model = current_app.config['OPENAI_MODEL']
    model_details = current_app.config['OPENAI_MODEL_DETAILS'].get(model)
    if not model_details:
        raise NotImplementedError(f"No model details in config for model {model}.")
    tokens_per_message = model_details['tokens_per_message']
    tokens_per_name = model_details['tokens_per_name']
    encoding = _get_token_encoding()
    num_tokens = 0
    for message in messages:
        num_tokens += tokens_per_message
        for key, value in message.items():
            num_tokens += len(encoding.encode(value))
            if key == "name":
                num_tokens += tokens_per_name
    num_tokens += 5  # every reply is primed with <|start|>assistant<|message|>
    return num_tokens


def _get_token_encoding():
    try:
        return tiktoken.encoding_for_model(current_app.config.get('OPENAI_MODEL'))
    except KeyError:
        return tiktoken.get_encoding("cl100k_base")


def truncate_text_by_tokens(val: str, max_tokens: int) -> str:
    encoding = _get_token_encoding()
    tokens = encoding.encode(val)
    truncated_tokens = tokens[:max_tokens]
    truncated_text = encoding.decode(truncated_tokens)
    return truncated_text
