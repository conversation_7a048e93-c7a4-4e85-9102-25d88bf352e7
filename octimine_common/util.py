from abc import ABC
from typing import Callable, TypeVar, Generic
from flask import current_app
from math import ceil
from http import HTTPStatus
from octimine_common import messages
from octimine_common.exceptions import APIException


class PageError(APIException):
    def __init__(self, message: str):
        APIException.__init__(self, HTTPStatus.BAD_REQUEST, message)


class SortError(APIException):
    def __init__(self, message: str):
        APIException.__init__(self, HTTPStatus.BAD_REQUEST, message)


class SearchInfo:
    def __init__(self):
        self.new_search = False
        self.search_hash = None
        self.details = {}

    def to_dict(self):
        res = {'new_search': self.new_search, 'search_hash': self.search_hash}
        if self.details:
            res['details'] = self.details
        return res


class _PageBase:

    def __init__(self):
        self._total_hits = 0
        self._total_publications = 0
        self._complete = None

    @classmethod
    def is_enabled(cls):
        raise NotImplementedError()

    @property
    def number(self):
        raise NotImplementedError()

    @property
    def size(self):
        raise NotImplementedError()

    @property
    def from_item(self):
        raise NotImplementedError()

    @property
    def to_item(self):
        raise NotImplementedError()

    @property
    def last_page(self):
        raise NotImplementedError()

    @property
    def total_hits(self):
        return self._total_hits

    @total_hits.setter
    def total_hits(self, total_hits: int):
        self.set_total_hits(total_hits)

    @property
    def total_publications(self):
        return self._total_publications

    @total_publications.setter
    def total_publications(self, total_publications: int):
        self.set_total_publications(total_publications)

    @property
    def complete(self):
        return self._complete is None or self._complete

    @complete.setter
    def complete(self, complete):
        self._complete = complete

    def set_total_hits(self, total_hits: int):
        self._total_hits = total_hits

    def set_total_publications(self, total_publications: int):
        self._total_publications = total_publications

    def narrow_to(self, max_results: int):
        raise NotImplementedError()

    def to_dict(self):
        res = dict(total_hits=self.total_hits, last_page=self.last_page, current_page=self.number,
                   page_size=self.size, total_publications=self.total_publications)
        if self._complete is not None:
            res['complete'] = self._complete
        return res


class _NoPage(_PageBase):

    @classmethod
    def is_enabled(cls):
        return False

    @property
    def number(self):
        return 1

    @property
    def size(self):
        return self._total_hits

    @property
    def from_item(self):
        return 0

    @property
    def to_item(self):
        return None

    @property
    def last_page(self):
        return 1 if self.total_hits else 0

    def narrow_to(self, max_results: int):
        pass


class _Page(_PageBase):

    def __init__(self, number: int, size: int):
        super().__init__()
        if number <= 0:
            raise PageError("Page number %d not allowed" % (number,))
        if size <= 0:
            raise PageError("Page size %d not allowed" % (size,))
        self._number = number
        self._size = size
        self._max_results = None

    @classmethod
    def is_enabled(cls):
        return True

    @property
    def number(self):
        return self._number

    @property
    def size(self):
        return self._size

    @property
    def from_item(self):
        return (self.number - 1) * self.size

    @property
    def to_item(self):
        return self.number * self.size

    @property
    def last_page(self):
        total = min(self._total_hits, self._max_results) if self._max_results else self._total_hits
        return ceil(total / self.size)

    def narrow_to(self, max_results: int):
        self._max_results = max_results


class Page(_PageBase, ABC):

    def __new__(cls, number: int = None, size: int = None, disabled: bool = False):
        if disabled:
            return _NoPage()
        else:
            number = 1 if number is None else number
            size = current_app.config['DEFAULT_PAGE_SIZE'] if size is None else size
            return _Page(number, size)

    @classmethod
    def from_request(cls, req):
        disabled = cls._fetch_disabled(req)
        number = cls._fetch_number(req)
        size = cls._fetch_size(req)
        return Page(number, size, disabled)

    @classmethod
    def replace(cls, page, number: int = None, size: int = None, disabled: bool = None):
        number = number if number is not None else page.number
        size = size if size is not None else page.size
        disabled = disabled if disabled is not None else not page.is_enabled()
        return Page(number, size, disabled)

    @classmethod
    def _fetch_disabled(cls, req):
        return 'no_paging' in req.args and 'X-Octimine-Internal' in req.headers

    @classmethod
    def _fetch_number(cls, req):
        return req.args.get('page', type=int)

    @classmethod
    def _fetch_size(cls, req):
        return req.args.get('page_size', type=int)


class Sort:
    def __init__(self, sort_column: str = None, sort_order: str = None):
        self.sort_column = sort_column
        self.sort_order = sort_order or 'asc'
        if self.sort_order not in ["asc", "desc"]:
            raise SortError(messages.ERROR_SORT_ORDER % self.sort_order)

    def is_defined(self) -> bool:
        return self.sort_column is not None

    @staticmethod
    def from_request(req, column_name: str = 'sort_by', order_name: str = 'sort_order'):
        sort_column = req.args.get(column_name, type=str)
        sort_order = req.args.get(order_name, type=str)
        return Sort(sort_column, sort_order)

    @staticmethod
    def from_dict(d):
        return Sort(d.get('sort_by'), d.get('sort_order'))

    def to_dict(self):
        return dict(sort_by=self.sort_column, sort_order=self.sort_order)


_T = TypeVar('_T')


class Paginator(Generic[_T]):

    def __init__(self, fetch_page: Callable[[_NoPage | _Page | Page], _T], *, page_size: int = 25):
        self._fetch_page = fetch_page
        self._page_number = 1
        self._last_page = None
        self._page_size = page_size

    def has_next(self) -> bool:
        return self._last_page is None or self._page_number <= self._last_page

    def next(self) -> _T:
        page = Page(self._page_number, self._page_size)
        value = self._fetch_page(page)
        self._page_number = page.number + 1
        self._last_page = page.last_page
        return value
