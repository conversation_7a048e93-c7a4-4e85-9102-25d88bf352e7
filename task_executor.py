from celery.schedules import crontab

from run import app

from app.background_tasks.send_subscription_change_emails import send_subscription_change_email_in_bg
from app.background_tasks.trials_report import send_trials_report_email_in_bg
from app.background_tasks.clean_old_user_logs import clean_old_user_logs_in_bg
from app.background_tasks.free_user_reminder_to_proceed_further import \
    send_free_users_proceed_further_email_in_bg
from app.background_tasks.convert_trial_users_to_free import \
    convert_trial_users_to_free_in_bg
from app.background_tasks.sync_users_to_hubspot_contacts import sync_users_to_hubspot_contacts_in_bg
from app.background_tasks.update_task_status_overdue import update_task_status_overdue
from app.background_tasks.retry_stuck_monitor_runs import retry_stuck_runs
from app.background_tasks.remove_expired_collections import remove_expired_collections
from app.background_tasks.monitor.runs.results.schedule import schedule_runs
from app.background_tasks.monitor.runs.results.report import generate_report
from app.background_tasks.cleanup_orphans import cleanup_resource_orphans
from app.background_tasks.delete_blocked_users import delete_blocked_users_in_bg
from app.background_tasks.block_inactive_users import block_inactive_users_in_bg
from app.background_tasks.send_notification_emails import send_notification_emails_in_bg
from app.background_tasks.send_emails_for_pending_ratings import send_emails_for_pending_ratings_in_bg

celery = app.extensions['celery']


@celery.on_after_configure.connect
def setup_periodic_tasks(sender, **_):
    sender.add_periodic_task(
        crontab(hour=8, minute=0, day_of_week=5),
        send_trials_report_email_in_bg.s(),
    )
    sender.add_periodic_task(
        crontab(hour=0, minute=0, day_of_week='*'),
        clean_old_user_logs_in_bg.s(),
    )
    sender.add_periodic_task(
        crontab(hour='*', minute=0),
        send_subscription_change_email_in_bg.s(),
    )
    sender.add_periodic_task(
        crontab(hour=8, minute=0, day_of_week='*'),
        send_free_users_proceed_further_email_in_bg.s(),
    )
    sender.add_periodic_task(
        crontab(hour=0, minute=0),
        convert_trial_users_to_free_in_bg.s(),
    )
    sender.add_periodic_task(
        crontab(hour='*', minute='0', day_of_week='*'),
        retry_stuck_runs.s(),
    )

    # update task management status daily at midnight.
    sender.add_periodic_task(
        crontab(minute=0, hour=0),
        update_task_status_overdue.s(),
    )

    # remove expired collections daily at midnight.
    sender.add_periodic_task(
        crontab(minute=0, hour=0),
        remove_expired_collections.s(),
    )

    sender.add_periodic_task(
        crontab(**app.config['MONITOR_AUTO_SCHEDULING_CRONTAB']),
        schedule_runs.s()
    )

    # Sync users to hubspot contacts weekly on Saturday at 1:00 AM.
    sender.add_periodic_task(
        crontab(hour=1, minute=0, day_of_week=6),
        sync_users_to_hubspot_contacts_in_bg.s(),
    )

    sender.add_periodic_task(
        crontab(minute='30', hour='0'),
        cleanup_resource_orphans.s(),
    )

    if app.config['MONITOR_REPORT_CRONTAB']:
        sender.add_periodic_task(
            crontab(**app.config['MONITOR_REPORT_CRONTAB']),
            generate_report.s()
        )

    sender.add_periodic_task(
        crontab(hour="1", minute="15", day_of_week="6"),
        block_inactive_users_in_bg.s(),
    )
    sender.add_periodic_task(
        crontab(hour="2", minute="15", day_of_week="6"),
        delete_blocked_users_in_bg.s(),
    )

    sender.add_periodic_task(
        crontab(**app.config['SEND_NOTIFICATION_EMAILS_CRONTAB']),
        send_notification_emails_in_bg.s(),
    )

    sender.add_periodic_task(
        crontab(**app.config['SEND_MAILS_FOR_PENDING_RATINGS_CRONTAB']),
        send_emails_for_pending_ratings_in_bg.s(),
    )
