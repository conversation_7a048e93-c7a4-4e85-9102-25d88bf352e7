FROM python:3.12.10

ENV FLASK_ENV=development
ENV FLASK_APP=run.py
ENV APP_PORT=5000
ENV GUNICORN_APP=run:app
ENV GUNICORN_CONFIG="--preload --workers 8 --error-logfile /var/log/gunicorn-error.log --access-logfile /var/log/gunicorn-access.log --limit-request-line 0"

WORKDIR /api

RUN mkdir /celery_scheduler
RUN useradd appuser -m -d /home/<USER>/api /var/log/ /celery_scheduler

# System dependencies for Python SAML Toolkit by Onelogin
RUN apt update && apt install -y libxml2-dev libxslt-dev libxmlsec1-dev pkg-config
RUN pip install virtualenv==20.32.0 poetry

USER appuser
COPY --chown=appuser ./pyproject.toml ./poetry.lock /api/

SHELL ["/bin/bash", "-c"]
RUN virtualenv .venv
RUN source .venv/bin/activate &&  \
    poetry install --with=dev && \
    poetry add gunicorn

COPY --chown=appuser . /api
COPY --chown=appuser ./deploy/entrypoint.sh /api

ARG CI_COMMIT_REF_NAME=""
ARG CI_COMMIT_SHA=""

ENV PATH="/api/.venv/bin:${PATH}"
ENV GIT_BRANCH=${CI_COMMIT_REF_NAME}
ENV GIT_COMMIT_HASH=${CI_COMMIT_SHA}
ENV SCHEDULER_ENABLED=""

# Entrypoint, will create migrations and run gunicorn app server
RUN chmod u+x entrypoint.sh
ENTRYPOINT ["./entrypoint.sh"]
