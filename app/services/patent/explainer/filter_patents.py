import json

import octimine_common.openai as openai_utils


# --- 1. Define your Python Function (The "Tool") ---
# This is a regular Python function. In a real application, this might
# query a database, call another API, or access a file.
# For this example, it's just a simple mock function.

def filter_patents(order_id: str):
    """
    Gets the status of a specific order.

    Args:
        order_id (str): The unique identifier for the order.

    Returns:
        str: A JSON string describing the order status.
    """
    print(f"--- Function Called: filter_patents(order_id='{order_id}') ---")

    # Mock data for demonstration
    if order_id == "B-12345":
        status_info = {
            "order_id": order_id,
            "status": "Shipped",
            "shipping_carrier": "ExpressPost",
            "tracking_number": "EP-555-98765",
            "estimated_delivery": "2025-08-23"
        }
    elif order_id == "C-67890":
        status_info = {
            "order_id": order_id,
            "status": "Processing",
            "estimated_ship_date": "2025-08-22"
        }
    else:
        status_info = {
            "order_id": order_id,
            "status": "Not Found",
            "error": "No order found with that ID."
        }

    # The function must return a string, so we convert the dict to a JSON string.
    return json.dumps(status_info)


# --- 2. Describe the Tool to the AI Model ---
# We need to tell the model what tools it has available.
# This is done using a specific JSON schema format.
def run_filter_patents_function():
    # --- Setup ---
    # It's best practice to use an environment variable for your API key.
    # Make sure to set OPENAI_API_KEY in your environment.
    # If you can't, you can uncomment the line below and paste your key.
    # client = OpenAI(api_key="YOUR_OPENAI_API_KEY")
    try:
        client = openai_utils.create_client()
    except KeyError:
        print("ERROR: Please set the OPENAI_API_KEY environment variable.")
        exit()

    tools_definition = [
        {
            "type": "function",
            "function": {
                "name": "filter_patents",
                "description": "Filter the for ",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "patents": {
                            "type": "string",
                            "description": "List of patents including id and publication number. Eg: [{id: 49996002, publication_number: 'US20140032706A1'}]",
                        }
                    },
                    "required": ["order_id"],
                },
            },
        }
    ]

    # --- 3. Start the Conversation ---

    # The user's prompt. The model will see this and realize it needs to use a tool.
    user_prompt = "Hi there, can you please check the status of my order? The ID is B-12345."
    print(f"User: {user_prompt}\n")

    # We store the conversation history in a list of messages.
    messages = [{"role": "user", "content": user_prompt}]

    # --- First API Call ---
    # We send the user's message and the tool definitions to the model.
    response = client.chat.completions.create(
        model="gpt-4o",
        messages=messages,
        tools=tools_definition,
        tool_choice="auto",  # 'auto' lets the model decide if it should use a tool.
    )

    response_message = response.choices[0].message
    tool_calls = response_message.tool_calls

    # --- 4. Check if the Model Wants to Use a Tool ---
    if tool_calls:
        print("--- Model decided to call a function ---", response_message)
        # The model wants to call a function. We need to execute it.

        # Add the assistant's response (which contains the tool call request) to the history
        messages.append(response_message)

        # For this example, we'll just handle the first tool call.
        tool_call = tool_calls[0]
        function_name = tool_call.function.name
        function_args = json.loads(tool_call.function.arguments)

        # --- 5. Execute the Function ---
        # Call the actual Python function with the arguments provided by the model.
        if function_name == "filter_patents":
            function_response = filter_patents(
                order_id=function_args.get("order_id")
            )

            # --- 6. Send the Result Back to the Model ---
            # We add the function's output to the conversation history.
            # This tells the model what happened when we ran the function.
            messages.append(
                {
                    "tool_call_id": tool_call.id,
                    "role": "tool",
                    "name": function_name,
                    "content": function_response,
                }
            )

            # --- Second API Call ---
            # Now we call the model *again*, but this time with the function's result.
            print("--- Sending function result back to the model for a final answer ---\n")
            final_response = client.chat.completions.create(
                model="gpt-4o",
                messages=messages,  # Send the whole conversation history
            )

            # Print the final, natural-language response from the model.
            print(f"ChatGPT: {final_response.choices[0].message.content}")

    else:
        # If the model didn't need a tool, we just print its response.
        print(f"ChatGPT: {response_message.content}")
